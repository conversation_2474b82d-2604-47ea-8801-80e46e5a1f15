using System;
using System.Collections;
using System.Collections.Generic;
using FMODUnity;
using UnityEngine;
using UnityEngine.Rendering;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class HidePlatformEvent : LevelEvent
    {
        // Shadow_mode: set shadowCastingMode to ShadowCastingMode.ShadowsOnly when disabling the platform
        // Disable_Renderer: disable the renderer entirely when disabling the platform
        private enum HidingMode { SHADOW_MODE, DISABLE_RENDERER}

        // UNITY HOOKUPS

        [SerializeField] private List<Renderer> _normalPlatform = new List<Renderer>();
        [SerializeField] private List<Renderer> _hiddenPlatform = new List<Renderer>();

        [SerializeField] private AnimationCurve _flickerRateCurve = AnimationCurve.Linear(0f, 0f, 1f, 1f);
        [SerializeField] private float _duration = 3f;
        [SerializeField] private float _slowFlickerInterval = 0.4f;
        [SerializeField] private float _fastFlickerInterval = 0.15f;
        [SerializeField] private HidingMode _mode = HidingMode.SHADOW_MODE;

        [Space]
        [SerializeField] private EventReference _hidePlatformAudioRef;
        [SerializeField] private EventReference _showPlatformAudioRef;

        // OTHER FIELDS

        private bool _platformVisible = true;
        private Coroutine _hidePlatformPrecessCoroutine = null;

        // Only play audio for the latest hiding platform triggered so that they don't overlap
        private static HidePlatformEvent _latestHidePlatformTrigger = null;

        // PROPERTIES

        public float Duration => Mathf.Max(0.1f, _duration);


        // OTHER METHODS

        protected override void Awake()
        {
            base.Awake();

            // ventReference can only assign Path in Editor mode which is causing this to break when
            // building addressables. To have the platforms hide with audio, we need to set the Event Reference using
            // the SerializeField.
#if UNITY_EDITOR
            if (_hidePlatformAudioRef.IsNull)
            {
                _hidePlatformAudioRef = new EventReference(){Path = "event:/SFX/TerrainFeatures/HidePlatform"};
            }
            if (_showPlatformAudioRef.IsNull)
            {
                _showPlatformAudioRef = new EventReference(){Path = "event:/SFX/TerrainFeatures/ShowPlatform"};
            }
#endif

            ResetPlatform();
        }

        protected override void Reset()
        {
            base.Reset();

            _hidePlatformAudioRef = FMODUnity.RuntimeManager.PathToEventReference("event:/SFX/TerrainFeatures/HidePlatform");
            _showPlatformAudioRef = FMODUnity.RuntimeManager.PathToEventReference("event:/SFX/TerrainFeatures/ShowPlatform");
        }

        public override void TriggerEvent(bool initialization = false)
        {
            HidePlatform();
        }

        public void HidePlatform()
        {
            if (!_platformVisible)
            {
                return;
            }

            _platformVisible = false;
            _hidePlatformPrecessCoroutine = StartCoroutine(HidePlatformProcess());
        }

        public void ResetPlatform()
        {
            if (_hidePlatformPrecessCoroutine != null)
            {
                StopCoroutine(_hidePlatformPrecessCoroutine);
                _hidePlatformPrecessCoroutine = null;
            }

            SetPlatformEnabled(true);
            SetPlatformRenderer(true);
            _platformVisible = true;
        }

        private void SetPlatformEnabled(bool isEnabled)
        {
            foreach (Renderer platformRenderer in _normalPlatform)
            {
                platformRenderer.gameObject.SetActive(isEnabled);
            }

            foreach (Renderer platformRenderer in _hiddenPlatform)
            {
                platformRenderer.gameObject.SetActive(isEnabled);
            }
        }

        private void SetPlatformRenderer(bool isVisible)
        {
            foreach (Renderer platformRenderer in _normalPlatform)
            {
                if (_mode == HidingMode.SHADOW_MODE)
                {
                    platformRenderer.shadowCastingMode = isVisible ? ShadowCastingMode.On : ShadowCastingMode.ShadowsOnly;
                }
                else if (_mode == HidingMode.DISABLE_RENDERER)
                {
                    platformRenderer.enabled = isVisible;
                }

            }
            foreach (Renderer platformRenderer in _hiddenPlatform)
            {
                platformRenderer.enabled = !isVisible;
            }
        }

        private IEnumerator HidePlatformProcess()
        {
            if (_hidePlatformPrecessCoroutine != null)
            {
                yield break;
            }

            _latestHidePlatformTrigger = this;

            float time = 0f;
            float timeSinceLastflicker = 0f;
            bool isVisible = true;

            SetPlatformEnabled(true);
            SetPlatformRenderer(true);

            while (time < Duration)
            {
                float timePercent = time / Duration;

                float flickerRate = Mathf.Lerp(_slowFlickerInterval, _fastFlickerInterval, _flickerRateCurve.Evaluate(timePercent));

                if (timeSinceLastflicker >= flickerRate)
                {
                    isVisible = !isVisible;
                    timeSinceLastflicker = 0f;
                    SetPlatformRenderer(isVisible);

                    if (_latestHidePlatformTrigger == this)
                    {
                        if (isVisible)
                        {
                            RuntimeManager.PlayOneShot(_showPlatformAudioRef);
                        }
                        else
                        {
                            RuntimeManager.PlayOneShot(_hidePlatformAudioRef);
                        }
                    }
                }

                time += Time.deltaTime;
                timeSinceLastflicker += Time.deltaTime;

                yield return null;
            }

            SetPlatformEnabled(false);
            _hidePlatformPrecessCoroutine = null;
        }
    }
}