// Copyright Isto Inc.
using Isto.Core.Cameras;
using Isto.Core.Data;
using Isto.Core.Platforms.Xbox;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml.Serialization;
using UnityEngine;
using Isto.Core.Installers;
using Isto.Core.Speedrun;

#if PLATFORM_GAMECORE
using Isto.Core.Localization;
using Isto.Core.Platforms;
using Isto.Core.UI;
using System.Collections;
using Unity.GameCore;
using Zenject;
#endif

namespace Isto.GTW.XBox
{
    /// <summary>
    /// Controls the saving and loading of data for all game data.
    /// In the case of this implementation, the data is always tied to a user on the console. (and this is how it backs up to the cloud)
    /// So we need to know who's data we want before fetching it.
    /// </summary>
    public class XBoxGameData : MonoBehaviour, IGameData, IDataLoadCompleteHandler
    {
        // UNITY HOOKUP

        [SerializeField] private bool _allowScreenshots = true;
        [SerializeField] private bool _allowValidation = true;


        // OTHER FIELDS

        public static readonly long MaximumSaveSpaceInBytes = 256 * 1024 * 1024; // 256 MB //268435456;
        public static readonly int MaximumNumberOfRegularSaveSlots = 50;
        public static readonly string SaveSlotContainerPrefix = "SaveSlot";
        public static readonly string CustomControlsMappingSaveContainer = "CustomControls";
        public static readonly string CustomControlsBlobName = "CustomControlsData";

        private string _thumbnailBlobName = "thumb.png";

        private bool _anAsyncOperationIsActive = false;

        private IGameData.GameDataLocation _saveSlotLocation = IGameData.GameDataLocation.UserAccount; // not sure this one should be injected - XboxGameData does not accept any other options

        private bool _initialized;
        private DataManager[] _managers;
        private XmlSerializer[] _serializers;


        // PROPERTIES

        public IGameData.GameDataLocation CurrentSaveLocation => _saveSlotLocation;
        public int MaximumSaveSlots => MaximumNumberOfRegularSaveSlots;


#if PLATFORM_GAMECORE
        // INJECTION
        private string _baseFilename;
        private UIModalPopup _errorPopup;
        private XboxPlatformTools _platformTools;
        private SpeedrunSettings _speedrunSettings;

        // Methods
        [Inject]
        public void Inject([Inject(Id = "SaveSlotLocation")] IGameData.GameDataLocation saveSlotLocation,
                           [Inject(Id = InjectId.SAVE_FILE_SUFFIX)] string saveFile,
                           SpeedrunSettings speedrunSettings,
                            [InjectOptional] UIModalPopup errorPopup,
                            IPlatformTools platformTools)
        {
            _baseFilename = saveFile;
            _errorPopup = errorPopup;
            //_saveSlotLocation = saveSlotLocation;
            _speedrunSettings = speedrunSettings;

            _platformTools = platformTools as XboxPlatformTools;
            Debug.Assert(_platformTools != null, "IPlatformTools are expected to be of type XboxPlatformTools if XBoxGameData exists");
        }
#endif


        // LIFECYLE EVENTS

        // Note: This interface method doesn't work the standard way.
        // For PC Data there is currently a hack in place, which we'll need to clean up eventually.
        // For xbox it's a bit more annoying to arrange, and it doesn't matter as much, so init will just be called
        // whenever the first time the managers are needed. There may or may not be a slight spike in duration for
        // that call but then the following ones will be lean.
        public void OnDataLoadComplete()
        {
            Initialize();
        }


        // ACCESSORS

        public string GetContainerName(int slotNumber)
        {
            return $"{SaveSlotContainerPrefix}{slotNumber}";
        }

        private int GetSaveSlotNumberFromName(string containerName)
        {
            if (!containerName.StartsWith(SaveSlotContainerPrefix) || !(containerName.Length > SaveSlotContainerPrefix.Length))
                return -1;

            string number = containerName.Substring(SaveSlotContainerPrefix.Length);
            int slotNb = int.Parse(number);
            return slotNb;
        }


        // OTHER METHODS

        // Several DataManagers tend to be part of the Player Essentials so we can't do this initialization until
        // a level is loaded.
        // Note however that as long as we rely on this initialization (which can be heavy which is why we moved
        // it at loading time) we have to load all DataManagers by the time OnDataLoadComplete happens, they can't
        // be added on the fly. (this is probably fine since the managers themselves should be lightweight)
        private void Initialize()
        {
            if (_initialized)
            {
                // Check if any data managers have been destroyed as we change scenes or something
                for (int i = 0; i < _managers.Length; i++)
                {
                    if (_managers[i] == null)
                    {
                        _initialized = false;
                        break;
                    }
                }
            }

            if (_initialized)
                return;

            _managers = FindObjectsOfType<DataManager>();
            _serializers = new XmlSerializer[_managers.Length];
            for (int i = 0; i < _managers.Length; i++)
            {
                object sampleSaveData = _managers[i].GetSampleSaveData();
                // Hope that the extra types won't change over the course of game progress. I suspect they won't
                // (DataManagers should be declaring these lists at compile time I think?) but I can't do a quick check
                // right now - there are no Extra Types used in GTW at least so for now it's not a problem.
                // If it does happen, then maybe we can set the PCGameData dirty and reinitialize.
                Type[] extraTypes = _managers[i].GetSaveExtraTypes();
                _serializers[i] = new XmlSerializer(sampleSaveData.GetType(), extraTypes);
            }

            _initialized = true;
        }

        public void SaveGameData(int saveSlot, bool createBackup, Action<bool> onSaveComplete = null)
        {
            string container = GetContainerName(saveSlot);

            SaveGameData(container, onSaveComplete);
        }

        public void SaveGameMetaData(int saveSlot, GameStateSaveData saveData, Action<bool> onSaveComplete)
        {
            XmlSerializer serializer = new XmlSerializer(typeof(GameStateSaveData));
            StringBuilder stringBuilder = new StringBuilder();
            using (StringWriter writer = new StringWriter(stringBuilder)) // throws ArgumentNullException
            {
                serializer.Serialize(writer, saveData);
            }

            // From .NET API doc:
            // Since the StringWriter is closed, an exception will
            // be thrown if the Write method is called. However,
            // the StringBuilder can still manipulate the string.
            string stringData = stringBuilder.ToString();

            string container = GetContainerName(saveSlot);
            List<GDK_XGameSaveManager.Blob> blobs = new List<GDK_XGameSaveManager.Blob>();
            blobs.Add(new GDK_XGameSaveManager.Blob() { blobName = GameStateDataManager.FILE_NAME, dataToSave = System.Text.Encoding.UTF8.GetBytes(stringData) });

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, container, blobs, onSaveComplete));
#endif
        }

        public void LoadGameData(int saveSlot, Action<bool> onLoadComplete = null)
        {
            string container = GetContainerName(saveSlot);

            LoadGameData(container, onLoadComplete);
        }

        public void LoadGameMetaData(int saveSlot, Action<GameStateSaveData> onLoadComplete)
        {
            Debug.Log("loading metadata...");
            string container = GetContainerName(saveSlot);
            string blobName = GameStateDataManager.FILE_NAME;
            string[] blobNames = { blobName };
#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, container, blobNames,
                (Dictionary<string, string> result) =>
                {
                    GameStateSaveData stateData = null;
                    if (result != null)
                    {
                        string data = result[blobName];
                        if (data != null)
                        {
                            using (StringReader reader = new StringReader(data)) // throws ArgumentNullException
                            {
                                try
                                {
                                    XmlSerializer serializer = new XmlSerializer(typeof(GameStateSaveData));
                                    stateData = (GameStateSaveData)serializer.Deserialize(reader); // throws InvalidOperationException
                                }
                                catch (Exception e)
                                {
                                    // Don't think I want to show error popups from loading metadata as we're going to be trying to get it from all the slots as we're just navigating menus
                                    // If the metadata is bad reflect this as a bad slot in the menu (or not at all) - only when loading the game proper, then pop the error dialog
                                    Debug.LogError($"Error reading save data from save slot {saveSlot}.  Exception: {e.Message}\n{e.InnerException?.Message}");
                                }
                            }
                        }
                        else
                        {
                            Debug.LogError($"Data container {container} exists for slot#{saveSlot} but GameStateSaveData seems empty");
                        }
                    }
                    else
                    {
                        Debug.LogError($"Data container {container} exists for slot#{saveSlot} but no GameStateSaveData found");
                    }

                    onLoadComplete?.Invoke(stateData);
                }));
#endif
        }

        /// <summary>
        /// Finds all DataManagers in loaded scene and calls their Save functions.
        /// </summary>
        /// <param name="saveContainer">User cloud save container where the files for the save will reside</param>
        private void SaveGameData(string saveContainer, Action<bool> onSaveComplete)
        {
            Initialize();

            List<GDK_XGameSaveManager.Blob> blobs = new List<GDK_XGameSaveManager.Blob>();
            string[] blobNames = new string[_managers.Length];
            object[] dataObjects = new object[_managers.Length];
            string[] dataStrings = new string[_managers.Length];
            string data;

            for (int i = 0; i < _managers.Length; i++)
            {
                blobNames[i] = _managers[i].BlobName;
                object saveData;
                _managers[i].Save(out saveData);
                dataObjects[i] = saveData;

                using (StringWriter textWriter = new StringWriter()) // creates a new StringBuilder internally, using CultureInfo.CurrentCulture
                {
                    _serializers[i].Serialize(textWriter, saveData);
                    data = textWriter.ToString();
                }

                dataStrings[i] = data;

                blobs.Add(new GDK_XGameSaveManager.Blob { blobName = _managers[i].BlobName, dataToSave = System.Text.Encoding.UTF8.GetBytes(data) });
            }

            if (_allowScreenshots)
            {
                byte[] thumbnailData;
                SaveScreenShot(saveContainer, out thumbnailData);
                if (thumbnailData != null)
                {
                    blobs.Add(new GDK_XGameSaveManager.Blob { blobName = _thumbnailBlobName, dataToSave = thumbnailData });
                }
            }

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, saveContainer, blobs,
                (result) =>
                {
                    if (!result)
                    {
                        onSaveComplete?.Invoke(false);
                        return;
                    }

                    // If the operation succeeded, we still should validate the stored info before confirming that success.
                    // This is more of a risky area on PC where a ton of things can go wrong. Using microsoft's game save provider
                    // on console lets them do all the normal error handling in the background. But it is not realistic to assume
                    // that it cannot ever fail.
                    // However, this validation can be heavy, so we've made it optional.
                    if (!_allowValidation)
                    {
                        onSaveComplete?.Invoke(true);
                    }
                    else
                    {
                        StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, saveContainer, blobNames,
                            (Dictionary<string, string> result) =>
                            {
                                bool success = result != null;
                                if (success)
                                {
                                    string loadedData = null;
                                    for (int i = 0; i < _managers.Length; i++)
                                    {
                                        object prevData = dataObjects[i];

                                        DataManager currentManager = _managers[i];
                                        loadedData = result[currentManager.BlobName];

                                        if (!string.IsNullOrEmpty(loadedData))
                                        {
                                            if (loadedData != dataStrings[i])
                                            {
                                                success = false;
                                                Debug.LogError($"Error checking data container {saveContainer} for save data: string for {currentManager.BlobName} does not match sent data.");
                                                _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception("Save Failure. There is a problem with the newly saved game. You may be able to load your game but some things could be missing."));
                                                break;
                                            }

                                            // On PC we also do currentManager.Validate in hopes of confirming that the data is reinterpreted correctly.
                                            // This in-depth validation causes a 10s freeze on xbox one S (with an slightly advanced game, with a good amount of automation).
                                            // The automation data manager makes up almost all of it (25% caused by the deserialization, 75% by the validation).
                                            // And this additional validation is probably overkill. So here, we don't do it.
                                        }
                                        else
                                        {
                                            Debug.LogError($"Error checking data container {saveContainer} for save data: {currentManager.BlobName} is missing.");
                                            _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception("There is missing data in the newly saved game. You may be able to load your game but some things could be missing."));
                                        }

                                        loadedData = null;
                                    }

                                    onSaveComplete?.Invoke(success);
                                }
                                else
                                {
                                    Debug.LogError($"Error - failed to create save data in container {saveContainer}.");
                                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception("Game save created but appears empty. Please retry."));
                                }
                            }));
                    }
                }));
#endif
        }

        /// <summary>
        /// Finds all DataManagers in loaded scene and calls their Load functions.
        /// </summary>
        /// <param name="saveContainer">Directory where the files for the save reside</param>
        private void LoadGameData(string saveContainer, Action<bool> onLoadComplete)
        {
            Initialize();

            string[] blobNames = new string[_managers.Length];
            for (int i = 0; i < _managers.Length; i++)
            {
                blobNames[i] = _managers[i].BlobName;
            }

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, saveContainer, blobNames,
            (Dictionary<string, string> result) =>
            {
                bool success = result != null;
                if (success)
                {
                    string data = null;
                    for (int i = 0; i < _managers.Length; i++)
                    {
                        DataManager currentManager = _managers[i];
                        data = result[currentManager.BlobName];

                        if (!string.IsNullOrEmpty(data))
                        {
                            using (StringReader reader = new StringReader(data)) // throws ArgumentNullException
                            {
                                currentManager.Load(reader);
                            }
                        }
                        else
                        {
                            Debug.LogError($"Error checking data container {saveContainer} for save data: {currentManager.BlobName} is missing.");
                            _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception("There is missing data in this save file. You may be able to load your game but some things could be missing."));
                        }

                        data = null;
                    }
                }
                else
                {
                    Debug.LogError($"Error - failed to load save data from container {saveContainer}.");
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception("Could not load the save file."));
                }

                onLoadComplete?.Invoke(success);
            }));
#endif
        }

        // From IGameData
        public void DuplicateGameData(int sourceSaveSlot, int destinationSaveSlot, Action<bool> onDuplicationComplete)
        {
            Debug.Log("DuplicateGameData start");
            string sourceContainer = GetContainerName(sourceSaveSlot);
            string destinationContainer = GetContainerName(destinationSaveSlot);

            // This gives us an instance of all the managers we would want to use to upgrade, so it's exactly what we need to fetch
            //List<DataManager> managers = SaveDataMigrationManager.CreateGOWithDataManagers();

            GameObject managers = new GameObject("Managers");

            // We should target all the managers, not just those we need for the upgrade, otherwise the backup will be a partial slot
            // (this list is based on what's in the zenject dependencies we install)
            List<DataManager> dataManagers = new List<DataManager>();
            dataManagers.Add(managers.AddComponent<PlayerDataManager>());
            dataManagers.Add(managers.AddComponent<GameStateDataManager>());

            //DataManager[] managers = Resources.FindObjectsOfTypeAll<DataManager>();
            string[] blobNames = new string[dataManagers.Count];
            for (int i = 0; i < dataManagers.Count; i++)
            {
                blobNames[i] = dataManagers[i].BlobName;
            }

#if PLATFORM_GAMECORE
            if (!_allowScreenshots)
            {
                // Only duplicate the save data
                DuplicateSaveDataInternal(sourceContainer, destinationContainer, blobNames, onDuplicationComplete);
            }
            else
            {
                // Simpler to handle this copy in 2 steps since we have 2 types of data
                // First get thumbnail
                LoadThumbnailForSaveSlot(sourceSaveSlot, (texture) =>
                {
                    if (texture == null)
                    {
                        onDuplicationComplete?.Invoke(false);
                        return;
                    }

                    // Copy thumbnail over
                    byte[] thumbnailData = texture.EncodeToPNG();
                    List<GDK_XGameSaveManager.Blob> imageBlob = new List<GDK_XGameSaveManager.Blob>();
                    imageBlob.Add(new GDK_XGameSaveManager.Blob { blobName = _thumbnailBlobName, dataToSave = thumbnailData });
                    StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, destinationContainer, imageBlob,
                    (imageSaveSuccess) =>
                    {
                        if (!imageSaveSuccess)
                        {
                            onDuplicationComplete?.Invoke(imageSaveSuccess);
                            return;
                        }

                        DuplicateSaveDataInternal(sourceContainer, destinationContainer, blobNames, onDuplicationComplete);
                    }));
                });
            }
#endif
        }

        private void DuplicateSaveDataInternal(string sourceContainer, string destinationContainer, string[] blobNames, Action<bool> onDuplicationComplete)
        {
#if PLATFORM_GAMECORE
            // Then get save data
            StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, sourceContainer, blobNames,
            (Dictionary<string, string> result) =>
            {
                bool dataLoadSuccess = result != null;
                if (!dataLoadSuccess)
                {
                    onDuplicationComplete?.Invoke(dataLoadSuccess);
                    return;
                }

                List<GDK_XGameSaveManager.Blob> dataBlobs = new List<GDK_XGameSaveManager.Blob>();
                foreach (KeyValuePair<string, string> pair in result)
                {
                    dataBlobs.Add(new GDK_XGameSaveManager.Blob { blobName = pair.Key, dataToSave = System.Text.Encoding.UTF8.GetBytes(pair.Value) });
                }

                // And send that too over to the new container
                StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, destinationContainer, dataBlobs,
                (dataSaveSuccess) =>
                {
                    Debug.Log("DuplicateGameData finish");
                    onDuplicationComplete?.Invoke(dataSaveSuccess);
                }));
            }));
#endif
        }

        // From IGameData
        public void UpgradeGameData(int saveSlot, string targetSaveVersion, Action<bool> onUpgradeComplete)
        {
            Debug.Log("UpgradeGameData start");
            string saveContainer = GetContainerName(saveSlot);
            DataManager[] managers = Resources.FindObjectsOfTypeAll<DataManager>();
            string[] blobNames = new string[managers.Length];
            for (int i = 0; i < managers.Length; i++)
            {
                blobNames[i] = managers[i].BlobName;
            }

#if PLATFORM_GAMECORE
            /*
            //Need to obtain the metadata as we'll need to use this for reference during the upgrade
            LoadGameMetaData(saveSlot, (gameStateData) =>
            {
                if (gameStateData == null)
                {
                    onUpgradeComplete?.Invoke(false);
                    return;
                }

                //Need to obtain the save data that we want to upgrade
                StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, saveContainer, blobNames,
                (Dictionary<string, string> result) =>
                {
                    bool success = result != null;
                    if (!success)
                    {
                        onUpgradeComplete?.Invoke(success);
                        return;
                    }

                    List<GDK_XGameSaveManager.Blob> dataBlobs = new List<GDK_XGameSaveManager.Blob>();

                    // Note: This method uses delegates to interact with the correct in/out parameters in context, but it's not async
                    SaveDataMigrationManager.UpgradeSaveFilesInLocationToLatestVersion(targetSaveVersion, gameStateData,
                    (manager) =>
                    {
                        string data = result[manager.BlobName];

                        if (string.IsNullOrEmpty(data))
                            return null;

                        // UpgradeSaveFilesInLocationToLatestVersion should dispose of this when done with it
                        StringReader reader = new StringReader(data); // throws ArgumentNullException
                        return reader;
                    },
                    (manager, serializer, data) =>
                    {
                        StringBuilder builder = new StringBuilder();
                        using (StringWriter writer = new StringWriter(builder)) // throws ArgumentNullException
                        {
                            serializer.Serialize(writer, data);
                            string updatedData = builder.ToString();
                            dataBlobs.Add(new GDK_XGameSaveManager.Blob { blobName = manager.BlobName, dataToSave = System.Text.Encoding.UTF8.GetBytes(updatedData) });
                        }
                    });

                    // Update the container with the upgraded data
                    StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, saveContainer, dataBlobs,
                    (dataSaveSuccess) =>
                    {
                        Debug.Log("UpgradeGameData finish");
                        onUpgradeComplete?.Invoke(dataSaveSuccess);
                    }));
                }));
            });*/
#endif
        }


        private Coroutine hasSaveDataCR = null;
        /// <summary>
        /// Checks if save files exist.
        /// </summary>
        /// <returns>True if files are in the save folder, false otherwise.</returns>
        public void HasAnySaveData(Action<bool> onCheckComplete = null)
        {
#if PLATFORM_GAMECORE
            if (!_platformTools.IsInitialized)
            {
                Debug.LogError("Xbox Platform Tools not initialized, can't get XboxGameData");
                return;
            }

            if (hasSaveDataCR != null)
            {
                StopCoroutine(hasSaveDataCR);
            }

            hasSaveDataCR = StartCoroutine(CO_ManageGetContainersForCurrentUser(_platformTools.UserManager.LastSelectedUser,
                (containers) =>
                {
                    bool dataExists = false;
                    if (containers != null && containers.Length > 0)
                    {
                        for (int i = 0; i < containers.Length; i++)
                        {
                            int slot = GetSaveSlotNumberFromName(containers[i].Name);
                            if (slot != -1)
                            {
                                // proof found, that's sufficient, ignore the rest.
                                dataExists = true;
                                break;
                            }
                        }
                    }

                    onCheckComplete.Invoke(dataExists);
                    hasSaveDataCR = null;
                }));
#endif
        }

        public void HasSaveData(int slotNumber, Action<bool> onCheckComplete = null)
        {
            if (slotNumber > MaximumNumberOfRegularSaveSlots)
            {
                onCheckComplete?.Invoke(true); // It's a special save slot
                return;
            }

            if (slotNumber < 0)
            {
                onCheckComplete?.Invoke(false);
                return;
            }

            string container = GetContainerName(slotNumber);

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageCheckContainerExistsForCurrentUser(_platformTools.UserManager.LastSelectedUser, container, onCheckComplete));
#endif
        }

        public void GetAllExistingSaveSlots(Action<List<int>> onListReady = null)
        {
#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageGetContainersForCurrentUser(_platformTools.UserManager.LastSelectedUser,
                (containers) =>
                {
                    List<int> foundSlots = new List<int>();
                    string[] separator = { SaveSlotContainerPrefix };
                    foreach (XGameSaveContainerInfo container in containers)
                    {
                        if (container.Name == CustomControlsMappingSaveContainer)
                        {
                            continue;
                        }

                        string slotName = container.Name;
                        string[] parts = slotName.Split(separator, System.StringSplitOptions.None);

                        if (parts.Length == 0)
                        {
                            Debug.LogError($"Unexpected results in save slot lookup algorithm: no separator (offender slot={slotName})");
                        }
                        else
                        {
                            if (int.TryParse(parts[parts.Length - 1], out int slotNum))
                            {
                                foundSlots.Add(slotNum);
                            }
                            else
                            {
                                Debug.LogError($"Unexpected results in save slot lookup algorithm: no number (offender slot={slotName})");
                            }
                        }
                    }

                    onListReady?.Invoke(foundSlots);
                }));
#endif
        }

        public void ClearAllSaveData(Action<bool> onClearComplete = null)
        {
#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageDeleteAllSavesForCurrentUser(_platformTools.UserManager.LastSelectedUser, onClearComplete));
#endif
        }

        public void ClearSaveData(int slotNumber, Action<bool> onClearComplete = null)
        {
            string container = GetContainerName(slotNumber);

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageDeleteSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, container, onClearComplete));
#endif
        }

        private void SaveScreenShot(string saveDirectory, out byte[] data)
        {
            data = null;

            CameraController cameraControl = FindObjectOfType<CameraController>();
            if (cameraControl != null)
            {
                Texture2D screenCap = cameraControl.GetScreenCapture();

                if (screenCap != null)
                {
                    data = screenCap.EncodeToPNG();
                }
            }
            else
            {
                UnityEngine.Camera camera = UnityEngine.Camera.main;
                if (camera != null)
                {
                    Texture2D screenCap = camera.GetScreenCapture();
                    if (screenCap != null)
                    {
                        data = screenCap.EncodeToPNG();
                    }
                }
            }
        }

        public void LoadThumbnailForSaveSlot(int slotNumber, Action<Texture2D> onThumbnailReady = null)
        {
            if ((slotNumber >= 0))
            {
                string containerName = GetContainerName(slotNumber);
                string blobName = _thumbnailBlobName;
                string[] blobNames = { blobName };

#if PLATFORM_GAMECORE
                StartCoroutine(CO_ManageLoadImage(_platformTools.UserManager.LastSelectedUser, containerName, blobName,
                (bytes) =>
                {
                    Texture2D texture = new Texture2D(2, 2);

                    if (bytes != null && bytes.Length > 0)
                    {
                        texture.LoadImage(bytes);
                    }

                    onThumbnailReady?.Invoke(texture);
                }));
#endif
            }
        }

        public void GetLowestAvailableSlotNumber(Action<int> onSlotNumberFound = null)
        {
            GetAllExistingSaveSlots((list) =>
            {
                int result = GetLowestAvailableSlotNumber(list);

                onSlotNumberFound?.Invoke(result);
            });
        }

        // TODO: make sure the list is sorted so we can make a slightly more optimal algorithm?
        private int GetLowestAvailableSlotNumber(List<int> existingSlots)
        {
            int result = -1;

            if (existingSlots.Count < MaximumNumberOfRegularSaveSlots)
            {
                for (int i = 0; i < MaximumNumberOfRegularSaveSlots; i++)
                {
                    if (!existingSlots.Contains(i))
                    {
                        result = i;
                        Debug.Log($"GetLowestAvailableSlotNumber: lowest available slot number is {i} ");
                        break;
                    }
                }
            }
            else
                Debug.LogWarning("GetLowestAvailableSlotNumber: too many slots already!");

            return result;
        }

        public void GetNextAvailableSlotNumber(int previousSlot, Action<int> onNextSlotFound)
        {
            GetAllExistingSaveSlots((list) =>
            {
                int result = -1;

                for (int i = previousSlot; i < MaximumNumberOfRegularSaveSlots; i++)
                {
                    if (!list.Contains(i))
                    {
                        result = i;
                        break;
                    }
                }

                if (result == -1)
                    result = GetLowestAvailableSlotNumber(list);

                onNextSlotFound?.Invoke(result);
            });
        }

        /// <summary>
        /// Xbox limits us to 256mb per user (as well as a 16mb limit per file).
        /// </summary>
        public void GetRemainingSaveFileSpace(Action<long> onFileSpaceObtained = null)
        {
#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageCheckSpaceForCurrentUser(_platformTools.UserManager.LastSelectedUser, onFileSpaceObtained));
#endif
        }

        public void GetMaximumSaveFileSpace(Action<long> onMaxFileSpaceObtained = null)
        {
            onMaxFileSpaceObtained?.Invoke(MaximumSaveSpaceInBytes);
        }

        // Custom Controls Data

        // Note: this is called 2 times when you start the game.
        // I have no clue where the calls are coming from, lots of references seem to lead to unused methods.
        // Data checks are slow so we should rework the logic to try to avoid loading the same thing more than once
        // at the same time.
        public void HasCustomControlsData(Action<bool> onCheckComplete = null)
        {
#if PLATFORM_GAMECORE
            StartCoroutine(CO_WaitForInitializationThenCheckControlsBlob(onCheckComplete));
#endif
        }

        public void SaveCustomControlMappings(PlayerCustomControlsSaveData controlSaveData)
        {
            List<GDK_XGameSaveManager.Blob> blobs = new List<GDK_XGameSaveManager.Blob>();

            XmlSerializer serializer;

            serializer = DataManager.GetXMLSerializer(controlSaveData);

            string data;

            using (StringWriter textWriter = new StringWriter())
            {
                serializer.Serialize(textWriter, controlSaveData);
                data = textWriter.ToString();
            }

            blobs.Add(new GDK_XGameSaveManager.Blob { blobName = CustomControlsBlobName, dataToSave = System.Text.Encoding.UTF8.GetBytes(data) });

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageSaveForCurrentUser(_platformTools.UserManager.LastSelectedUser, CustomControlsMappingSaveContainer, blobs, (success) => { Debug.Log("Saving Custom Controls: " + success); }));
#endif
        }

        public void LoadCustomControlMappings(Action<PlayerCustomControlsSaveData> onLoadComplete)
        {
            string[] blobNames = new string[] { CustomControlsBlobName };

#if PLATFORM_GAMECORE
            StartCoroutine(CO_ManageLoadForCurrentUser(_platformTools.UserManager.LastSelectedUser, CustomControlsMappingSaveContainer, blobNames, (Dictionary<string, string> result) =>
            {
                PlayerCustomControlsSaveData stateData = null;
                if (result != null && result.ContainsKey(CustomControlsBlobName))
                {
                    string data = result[CustomControlsBlobName];

                    if (data != null)
                    {
                        using (StringReader reader = new StringReader(data))
                        {
                            XmlSerializer serializer = new XmlSerializer(typeof(PlayerCustomControlsSaveData));
                            stateData = (PlayerCustomControlsSaveData)serializer.Deserialize(reader);
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"No CustomController Mappings");
                    }
                }

                onLoadComplete?.Invoke(stateData);
            }));
#endif
        }

        // -- Xbox user/data example scene code... modified for our needs ---------------------------------------------------------

#if PLATFORM_GAMECORE

        private IEnumerator CO_WaitForInitializationThenCheckControlsBlob(Action<bool> onCheckComplete)
        {
            while (!_platformTools.IsInitialized || !_platformTools.DataInitialized)
                yield return null;

            bool containerExists = false;

            yield return CO_ManageCheckContainerExistsForCurrentUser(_platformTools.UserManager.LastSelectedUser, CustomControlsMappingSaveContainer, (result) => { containerExists = result; });

            if (!containerExists)
            {
                onCheckComplete?.Invoke(false);
                yield break;
            }

            yield return CO_ManageCheckBlobExistsForCurrentUser(_platformTools.UserManager.LastSelectedUser, CustomControlsMappingSaveContainer, CustomControlsBlobName, onCheckComplete);
        }

        private IEnumerator CO_ManageSaveForCurrentUser(GDK_XUserManager.XUserData xUserData, string containerName, List<GDK_XGameSaveManager.Blob> saveBlobs, Action<bool> onSaveComplete)
        {
            bool success = false;
            _anAsyncOperationIsActive = true;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to save for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onSaveComplete?.Invoke(success);
                yield break;
            }

            var hResult = PLHR.Invalid;

            foreach (GDK_XGameSaveManager.Blob blob in saveBlobs)
            {
                Debug.Log($"saving blob {blob.blobName}: {Loc.GetFileSizeLocalizedAndFormated(blob.dataToSave.Length)}");
            }

            // NOTE: this callback may happen from another thread so we need to minimise side effects
            _platformTools.SaveManager.RequestSave(containerName, saveBlobs, hr => hResult = hr);

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                Debug.Log($"saved blobs successfully!");
                success = true;
            }
            else
            {
                Debug.LogError($"saved failed:\t{PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;
            onSaveComplete?.Invoke(success);
        }

        private IEnumerator CO_ManageLoadForCurrentUser(GDK_XUserManager.XUserData xUserData, string containerName, string[] blobNames, Action<Dictionary<string, string>> onLoadComplete)
        {
            Dictionary<string, string> result = null;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to load for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onLoadComplete?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            var blobsLoadedForUser = (XGameSaveBlob[])null;

            _platformTools.SaveManager.RequestLoad
            (
                containerName, blobNames,
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                (hr, loadedBytes) =>
                {
                    hResult = hr;
                    blobsLoadedForUser = loadedBytes;
                }
            );

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult)
                && ((blobsLoadedForUser?.Length ?? 0) > 0))
            {
                result = new Dictionary<string, string>();
                for (int i = 0; i < blobsLoadedForUser.Length; i++)
                {
                    XGameSaveBlob blob = blobsLoadedForUser[i];
                    string blobName = blob.Info.Name;

                    string dataString = System.Text.Encoding.UTF8.GetString(blob.Data);
                    //Debug.LogError($"loaded blob {blobName} of size {Loc.GetFileSizeLocalizedAndFormated(blob.Info.Size)} ");
                    //Debug.Log($"blob data={dataString}");

                    result.Add(blobName, dataString);
                }
            }
            else
            {
                Debug.LogError($"load failed:\t{PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;

            onLoadComplete?.Invoke(result);
        }

        private IEnumerator CO_ManageLoadImage(GDK_XUserManager.XUserData xUserData, string containerName, string blobName, Action<byte[]> onLoadComplete)
        {
            byte[] result = null;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to load for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onLoadComplete?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            var blobsLoadedForUser = (XGameSaveBlob[])null;

            _platformTools.SaveManager.RequestLoad
            (
                containerName, new string[] { blobName },
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                (hr, loadedBytes) =>
                {
                    hResult = hr;
                    blobsLoadedForUser = loadedBytes;
                }
            );

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult)
                && ((blobsLoadedForUser?.Length ?? 0) > 0))
            {
                XGameSaveBlob blob = blobsLoadedForUser[0];
                result = blob.Data;

                //Debug.LogError($"loaded blob {blobName} of size {Loc.GetFileSizeLocalizedAndFormated(blob.Info.Size)} ");
            }
            else
            {
                Debug.LogError($"load failed:\t{PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;

            onLoadComplete?.Invoke(result);
        }

        private IEnumerator CO_ManageGetContainersForCurrentUser(GDK_XUserManager.XUserData xUserData, Action<XGameSaveContainerInfo[]> onContainersObtained)
        {
            yield return CO_WaitForDataToInitialize();

            XGameSaveContainerInfo[] result = null;

            if (_platformTools.SaveManager == null)
            {
                Debug.LogError("CO_ManageGetContainersForCurrentUser: SaveManager is null");
                onContainersObtained?.Invoke(null);
                yield break;
            }

            if (_platformTools.SaveManager.OwningUser == null)
            {
                Debug.LogError("CO_ManageGetContainersForCurrentUser: SaveManager.OwningUser is null");
                onContainersObtained?.Invoke(null);
                yield break;
            }

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to get containers for {xUserData.ToStringShort()}\nSaveManager.OwningUser is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onContainersObtained?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            XGameSaveContainerInfo[] containerArray = null;

            _platformTools.SaveManager.RequestGetContainers((hr, containers) =>
            {
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                hResult = hr;
                containerArray = containers;
            });

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                Debug.Log($"found {containerArray.Length} containers ");
                result = containerArray;
            }
            else
            {
                Debug.LogError($"error fetching list of containers: {PLHR.GetName(hResult)}");
            }

            onContainersObtained?.Invoke(result);
        }

        private IEnumerator CO_ManageCheckContainerExistsForCurrentUser(GDK_XUserManager.XUserData xUserData, string containerName, Action<bool> onResultObtained)
        {
            bool result = false;

            if (_platformTools.SaveManager == null)
                Debug.LogError("CO_ManageCheckContainerExistsForCurrentUser: SaveManager is null");
            if (_platformTools.SaveManager.OwningUser == null)
                Debug.LogError("CO_ManageCheckContainerExistsForCurrentUser: SaveManager.OwningUser is null");

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to check if container exists for {xUserData.ToStringShort()}\nSaveManager.OwningUser is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onResultObtained?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            bool response = false;

            _platformTools.SaveManager.RequestCheckContainerExists(containerName, (hr, exists) =>
            {
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                hResult = hr;
                response = exists;
            });

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                //Debug.LogError($"found container {containerName}");
                result = response;
            }
            else
            {
                Debug.LogError($"error fetching list of containers: {PLHR.GetName(hResult)}");
            }

            onResultObtained?.Invoke(result);
        }

        /// <summary>
        /// Assumes that the container exists, so prints an error if it doesn't.
        /// Call CO_ManageCheckContainerExistsForCurrentUser first if you're not sure it's there.
        /// </summary>
        private IEnumerator CO_ManageCheckBlobExistsForCurrentUser(GDK_XUserManager.XUserData xUserData, string containerName, string blobName, Action<bool> onResultObtained)
        {
            bool result = false;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to check if blob exists for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onResultObtained?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            bool response = false;

            _platformTools.SaveManager.RequestCheckContainerExists(containerName, (hr, exists) =>
            {
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                hResult = hr;
                response = exists;
            });

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.FAILED(hResult) || !response)
            {
                Debug.LogError($"could not fetch container {containerName}: {PLHR.GetName(hResult)} ");
                onResultObtained?.Invoke(result);
                yield break;
            }

            hResult = PLHR.Invalid;
            response = false;

            _platformTools.SaveManager.RequestCheckBlobExists(containerName, blobName, (hr, exists) =>
            {
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                hResult = hr;
                response = exists;
            });

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                //Debug.LogError($"blob check success on {blobName} - result is {PLHR.GetName(hResult)}, response={response}");
                result = response;
            }
            else
            {
                Debug.LogError($"error fetching blob: {PLHR.GetName(hResult)} ");
            }

            onResultObtained?.Invoke(result);
        }

        private IEnumerator CO_ManageCheckSpaceForCurrentUser(GDK_XUserManager.XUserData xUserData, Action<long> onFileSpaceObtained)
        {
            long result = -1;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to check space for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                onFileSpaceObtained?.Invoke(result);
                yield break;
            }

            _anAsyncOperationIsActive = true;

            var hResult = PLHR.Invalid;
            var userAllocationRemaining = default(long);

            _platformTools.SaveManager.RequestCheckSpace
            (
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                (hr, spaceAvailable) =>
                {
                    hResult = hr;
                    userAllocationRemaining = spaceAvailable;
                }
            );

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                //Debug.LogError($"space remaining: {userAllocationRemaining} bytes or {Loc.GetFileSizeLocalizedAndFormated(userAllocationRemaining)}");
                result = userAllocationRemaining;
            }
            else
            {
                Debug.LogError($"error fetching remaining space: {PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;

            onFileSpaceObtained?.Invoke(result);
        }

        private IEnumerator CO_ManageSaveExistsForCurrentUser(GDK_XUserManager.XUserData xUserData)
        {
            _anAsyncOperationIsActive = true;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to check save exists for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                yield break;
            }

            var hResult = PLHR.Invalid;
            var saveDataExists = false;

            _platformTools.SaveManager.RequestCheckSaveExists
            (
                // NOTE: this callback may happen from another thread so we need to minimise side effects
                (hr, saveExists) =>
                {
                    hResult = hr;
                    saveDataExists = saveExists;
                }
            );

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                Debug.Log($"save data {(saveDataExists ? "exists" : "not found")}");
                // return saveDataExists???
            }
            else
            {
                Debug.LogError($"error checking for save: {PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;
        }

        private IEnumerator CO_ManageDeleteAllSavesForCurrentUser(GDK_XUserManager.XUserData xUserData, Action<bool> onDeletionComplete)
        {
            bool success = false;

            _anAsyncOperationIsActive = true;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to delete save for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                yield break;
            }

            var hResult = PLHR.Invalid;

            // NOTE: this callback may happen from another thread so we need to minimise side effects
            _platformTools.SaveManager.RequestDeleteAll(hr => hResult = hr);

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                //Debug.LogError("save data deleted ok");
                success = true;
            }
            else
            {
                Debug.LogError($"error deleting save data: {PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;

            onDeletionComplete?.Invoke(success);
        }

        private IEnumerator CO_ManageDeleteSaveForCurrentUser(GDK_XUserManager.XUserData xUserData, string containerName, Action<bool> onDeletionComplete)
        {
            bool success = false;

            _anAsyncOperationIsActive = true;

            if (_platformTools.SaveManager.OwningUser != xUserData)
            {
                Debug.LogError($"Error: tried to delete save for {xUserData.ToStringShort()}\nSaveManager owning user is {_platformTools.SaveManager.OwningUser.ToStringShort()}");
                yield break;
            }

            var hResult = PLHR.Invalid;

            // NOTE: this callback may happen from another thread so we need to minimise side effects
            _platformTools.SaveManager.RequestDelete(containerName, hr => hResult = hr);

            while (PLHR.Invalid == hResult)
            {
                yield return null;
            }

            if (HR.SUCCEEDED(hResult))
            {
                //Debug.Log("save slot deleted ok");
                success = true;
            }
            else
            {
                Debug.LogError($"error deleting save: {PLHR.GetName(hResult)}");
            }

            _anAsyncOperationIsActive = false;

            onDeletionComplete?.Invoke(success);
        }

        private IEnumerator CO_WaitForDataToInitialize()
        {
            float waitTime = 10;

            while (!_platformTools.DataInitialized && waitTime > 0)
            {
                yield return null;

                waitTime -= Time.deltaTime;
            }

            if (waitTime <= 0)
                Debug.LogError("Past Maximum wait time for data to load.");
        }


#endif

        public void SaveSpeedrunTimes(SpeedrunTimerData timerData, Action<bool> onSaveComplete)
        {
            // atm feature not intended to be supported for gtw on xbox
            Debug.LogWarning("Speedrun times cannot be saved on xbox.");
            onSaveComplete?.Invoke(false);
        }

        public void LoadSpeedrunTimes(Action<SpeedrunTimerData> onLoadComplete)
        {
            // atm feature not intended to be supported for gtw on xbox
            Debug.LogWarning("Speedrun times cannot be saved or loaded on xbox.");
            onLoadComplete?.Invoke(null);
        }

        public void LoadGoldSplits(Action<SpeedrunTimerData> onLoadComplete)
        {
            // atm feature not intended to be supported for gtw on xbox
            Debug.LogWarning("Gold split speedrun times cannot be saved or loaded on xbox.");
            onLoadComplete?.Invoke(null);
        }
    }
}