// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Cameras;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Installers;
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using Isto.Core.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Security;
using System.Text;
using System.Threading;
using System.Xml;
using System.Xml.Serialization;
using UnityEngine;
//using UnityEngine.Profiling;
using Zenject;

using Debug = UnityEngine.Debug;

namespace Isto.Core.Platforms
{
    public class PCGameData : MonoBehaviour, IGameData, IDataLoadCompleteHandler
    {
        public static readonly int MaximumNumberOfRegularSaveSlots = 50;
        public static readonly string SaveSlotFolderPrefix = "SaveSlot";


        // UNITY HOOKUP

        [SerializeField] private Settings _settings;
        [SerializeField] private LocalizedString _upgradeSuffix; // UI/SaveLoad/BackupSuffix
        [SerializeField] private string _customControlsSaveFilename = "CustomControlMap";

        // TODO: these two features have been disabled for GetToWork before we refactored the class to use threads.
        // They will need refactoring before being used again.
        [SerializeField] private bool _allowScreenshots = true;
        [SerializeField] private bool _allowValidation = true;


        // OTHER FIELDS

        private string _thumbnailFilename = "\\thumb.png";

        private bool _initialized;
        private DataManager[] _managers;
        private XmlSerializer[] _serializers;


        // PROPERTIES

        public IGameData.GameDataLocation CurrentSaveLocation => _saveSlotLocation;
        public int MaximumSaveSlots => MaximumNumberOfRegularSaveSlots;


        // INJECTION

        protected string _baseFilename;
        private static UIModalPopup _errorPopup;
        private IGameData.GameDataLocation _saveSlotLocation;
        private SpeedrunSettings _speedrunSettings;

        [Inject]
        public void Inject([Inject(Id = "SaveSlotLocation")] IGameData.GameDataLocation saveSlotLocation,
                           [Inject(Id = InjectId.SAVE_FILE_SUFFIX)] string saveFile,
                           SpeedrunSettings speedrunSettings,
                           [InjectOptional] UIModalPopup errorPopup)
        {
            _saveSlotLocation = saveSlotLocation;
            _baseFilename = saveFile;
            _speedrunSettings = speedrunSettings;
            _errorPopup = errorPopup;

            if (_saveSlotLocation != IGameData.GameDataLocation.PersistentDataPath && _saveSlotLocation != IGameData.GameDataLocation.ResourcesFolder)
                Debug.LogError($"GameDataLocation {_saveSlotLocation} is not supported on PC yet");
        }


        // LIFECYLE EVENTS

        // This is to test some manipulations on xbox related save data, but I want to do these test on PC. delete this freely
        private void Start()
        {
            //I wanted to try to work with the string but note that in C++ I didn't need to remove the carriage returns to convert it
            //string concated = XBox.TestData.AUTOMATION_DATA.Replace("\n", "");
            //Debug.LogError(concated);
        }

        // Note: This interface method doesn't work the standard way. There is currently a hack in place for it.
        public void OnDataLoadComplete()
        {
            Initialize();
        }


        // OTHER METHODS

        // Several DataManagers tend to be part of the Player Essentials so we can't do this initialization until
        // a level is loaded.
        // Note however that as long as we rely on this initialization (which can be heavy which is why we moved
        // it at loading time) we have to load all DataManagers by the time OnDataLoadComplete happens, they can't
        // be added on the fly. (this is probably fine since the managers themselves should be lightweight)
        private void Initialize()
        {
            if (_initialized)
            {
                // Check if any data managers have been destroyed as we change scenes or something
                for (int i = 0; i < _managers.Length; i++)
                {
                    if (_managers[i] == null)
                    {
                        _initialized = false;
                        break;
                    }
                }
            }

            if (_initialized)
                return;

            _managers = FindObjectsOfType<DataManager>();
            _serializers = new XmlSerializer[_managers.Length];
            for (int i = 0; i < _managers.Length; i++)
            {
                object sampleSaveData = _managers[i].GetSampleSaveData();
                // Hope that the extra types won't change over the course of game progress. I suspect they won't
                // (DataManagers should be declaring these lists at compile time I think?) but I can't do a quick check
                // right now - there are no Extra Types used in GTW at least so for now it's not a problem.
                // If it does happen, then maybe we can set the PCGameData dirty and reinitialize.
                Type[] extraTypes = _managers[i].GetSaveExtraTypes();
                _serializers[i] = new XmlSerializer(sampleSaveData.GetType(), extraTypes);
            }

            _initialized = true;
        }

        public static string GetFilePath(string fileName, string saveLocation, string prefix)
        {
            return saveLocation + prefix + fileName;
        }

        public void SaveGameMetaData(int saveSlot, GameStateSaveData saveData, Action<bool> onSaveComplete)
        {
            string saveDirectory = GetSaveDirectory(saveSlot);
            string fullFilename = GetFilePath(_baseFilename, saveDirectory, GameStateDataManager.FILE_PREFIX);
            XmlSerializer serializer = new XmlSerializer(typeof(GameStateSaveData));
            bool success = WriteFile(fullFilename, serializer, saveData);
            onSaveComplete?.Invoke(success);
        }

        private Exception loadException = null;
        private GameStateSaveData loadedData = null;

        public void LoadGameMetaData(int saveSlot, Action<GameStateSaveData> onLoadComplete)
        {
            loadedData = null;
            string saveDirectory = GetSaveDirectory(saveSlot);
            string fullFilename = GetFilePath(_baseFilename, saveDirectory, GameStateDataManager.FILE_PREFIX);
            if (XMLFileExists(fullFilename))
            {
                loadException = null;
                System.Threading.ThreadPool.QueueUserWorkItem(state =>
                {
                    using (TextReader reader = ReadFile(fullFilename))
                    {
                        if (reader != null)
                        {
                            XmlSerializer serializer = new XmlSerializer(typeof(GameStateSaveData));
                            try
                            {
                                loadedData = (GameStateSaveData)serializer.Deserialize(reader); // throws InvalidOperationException
                            }
                            catch (InvalidOperationException e)
                            {
                                loadException = e;
                            }
                        }
                    }
                });

                StartCoroutine( WaitThreadForLoad(onLoadComplete));
            }
            else
            {
                onLoadComplete?.Invoke(loadedData);
            }
        }

        private IEnumerator WaitThreadForLoad(Action<GameStateSaveData> onLoadComplete)
        {
            while (loadedData == null && loadException == null)
            {
                yield return null;
            }

            if (loadException != null)
            {
                // Todo: messaging for user?
                //_errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                Debug.LogError("caught InvalidOperationException: " + loadException.InnerException.Message);
                onLoadComplete?.Invoke(null);
            }
            else
            {
                onLoadComplete?.Invoke(loadedData);
            }
        }

        /// <summary>
        /// If appropriate create a backup of the save slot where we're going to write data.
        /// Then, save the data, clean up the backup, and invoke the delegate to report on the success of the operation.
        /// When using the backup, if the operation fails in any way, we attempt to restore the save to what it was.
        /// </summary>
        public void SaveGameData(int saveSlot, bool createBackup, Action<bool> onSaveComplete)
        {
            if (createBackup)
            {
                GetNextAvailableSlotNumber(saveSlot, (backupSlotNum) =>
                {
                    if (backupSlotNum != -1)
                    {
                        DuplicateGameData(saveSlot, backupSlotNum, (backupSuccess) =>
                        {
                            if (backupSuccess)
                            {
                                LoadGameMetaData(backupSlotNum, (backupMetaData) =>
                                {
                                    if (backupMetaData == null)
                                    {
                                        Debug.LogError($"LoadSaveSlotMetaData returned null data for slot#{backupSlotNum} during backup process");
                                        return;
                                    }

                                    // TODO: ideally unify this logic with UIUpgradeSaveSlotSubState to remove duplication
                                    string suffix = $" {Loc.Get(_upgradeSuffix)}";

                                    if (backupMetaData.saveSlotName.Length > _settings.MaxCharactersInSaveName - suffix.Length)
                                        backupMetaData.saveSlotName = backupMetaData.saveSlotName.Substring(0, _settings.MaxCharactersInSaveName - suffix.Length);

                                    backupMetaData.saveSlotName += suffix;

                                    SaveGameMetaData(backupSlotNum, backupMetaData, (success) =>
                                    {
                                        Debug.Log($"UpdateSaveSlotMetaData to rename backup slot#{backupSlotNum} was a {(success ? "success" : "failure")}");
                                        // Try to save with or without backup
                                        InternalSaveGameData(saveSlot, backupSuccess ? backupSlotNum : -1, onSaveComplete);
                                    });
                                });
                            }
                            else
                            {
                                // Try to save with or without backup
                                InternalSaveGameData(saveSlot, backupSuccess ? backupSlotNum : -1, onSaveComplete);
                            }
                        });
                    }
                    else
                    {
                        // Try to save without backup
                        InternalSaveGameData(saveSlot, backupSlot: -1, onSaveComplete);
                    }
                });
            }
            else
            {
                InternalSaveGameData(saveSlot, backupSlot: -1, onSaveComplete);
            }
        }

        private void InternalSaveGameData(int saveSlot, int backupSlot, Action<bool> onSaveComplete)
        {
            Initialize();

            saveCount = _managers.Length;

            string saveDirectory = GetSaveDirectory(saveSlot);
            bool success = WriteSaveGameDataToPath(saveDirectory);

            // TODO: this has to move after the thread-blocking coroutine
            if (backupSlot != -1)
            {
                Debug.LogError("Logic for backup save and validation is not supported yet since we have upgraded the " +
                    "fundamental saving logic on PC! Keep this turned off until it can be fixed!");
                if (success)
                {
                    Debug.Log("Save success. Cleaning backup data.");
                    ClearSaveData(backupSlot,
                        (cleanupSuccess) =>
                        {
                            // If it fails just let it be, user can delete the slot later
                            Debug.Log($"Cleanup done on slot {backupSlot}. Succeeded? " + cleanupSuccess);
                        });
                }
                else
                {
                    Debug.Log("Save failure. Trying to restore backed up data");
                    DuplicateGameData(backupSlot, saveSlot,
                        (restoreSuccess) =>
                        {
                            if (restoreSuccess)
                            {
                                Debug.Log($"Backup restoration on slot {saveSlot} done. Succeeded? " + restoreSuccess);
                                // TODO: restore name? restoring backup data will have it end up with a "BACKUP" or "COPY" suffix
                                // But for now I think leaving it is better as it means there's a trace of the backup/restore process

                                ClearSaveData(backupSlot,
                                    (cleanupSuccess) =>
                                    {
                                        // If it fails just let it be, user can delete the slot later
                                        Debug.Log($"Cleanup done on slot {backupSlot}. Succeeded? " + cleanupSuccess);
                                    });
                            }
                            else
                            {
                                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new Exception($"Save failed. The save slot might be corrupted. A copy of your original save file should be available if you exit and re-enter the save menu."));
                                Debug.LogWarning($"Backup restoration on slot {saveSlot} failed. Backup slot should still exist.");
                            }
                        });
                }
            }

            StartCoroutine(WaitThreadForSave(onSaveComplete));
        }

        private static Exception saveException = null;
        private static int saveCount = 0;
        private IEnumerator WaitThreadForSave(Action<bool> onSaveComplete)
        {
            while (saveCount > 0 && saveException == null)
            {
                yield return null;
            }

            if (saveException != null)
            {
                Debug.LogError("caught InvalidOperationException: " + saveException.InnerException.Message);
                onSaveComplete?.Invoke(false);
            }
            else
            {
                onSaveComplete?.Invoke(true);
            }
        }

        public void LoadGameData(int saveSlot, Action<bool> onLoadComplete = null)
        {
            string saveDirectory = GetSaveDirectory(saveSlot);

            bool success = LoadGameData(saveDirectory);

            onLoadComplete?.Invoke(success);
        }

        /// <summary>
        /// Finds all DataManagers in loaded scene and calls their Save functions.
        /// </summary>
        /// <param name="saveDirectory">Directory where the files for the save will reside</param>
        private bool WriteSaveGameDataToPath(string saveDirectory)
        {
            Initialize();

            bool success = false;
            bool managerAllSuccess = true;
            for (int i = 0; i < _managers.Length; i++)
            {
                string fullFilename = GetFilePath(_baseFilename, saveDirectory, _managers[i].FilePrefix);
                object saveData;
                _managers[i].Save(out saveData);

                WriteFile(fullFilename, _serializers[i], saveData);

                if (_allowValidation)
                {
                    // Comparing strings is a lot simpler than comparing all the data objects after deserialization so it was added first.
                    // It seems more accurate to compare the actual data however, so we go on to validate the save data object,
                    // but we keep this check too as extra validation. Admitedly I'm not sure how useful it is to have both.
                    bool dataStringsAreTheSame = ValidateXmlFileAsString(fullFilename, _serializers[i], saveData);

                    // We end up reading the file twice but that's not the end of the world
                    using (TextReader reader = ReadFile(fullFilename))
                    {
                        if (reader != null)
                        {
                            bool dataObjectsAreTheSame = _managers[i].Validate(in reader, saveData);
                            Debug.Log($"File {fullFilename} loaded and checked against current data, and is {(dataObjectsAreTheSame ? "confirmed" : "not")} correct");
                            if (!dataObjectsAreTheSame || !dataStringsAreTheSame)
                            {
                                managerAllSuccess = false;
                            }
                        }
                        else
                        {
                            managerAllSuccess = false;
                            break;
                        }
                    }
                }
            }

            success = managerAllSuccess;

            if (_allowScreenshots && saveDirectory.Contains(SaveSlotFolderPrefix))
            {
                // For now I assume saving the screenshot does not need validation since we can live without it
                SaveScreenShot(saveDirectory);
            }

            return success;
        }

        private bool ValidateXmlFileAsString(string fullFilename, XmlSerializer serializer, object saveData)
        {
            using (TextReader reader = ReadFile(fullFilename))
            {
                string fromFile = null, fromLiveData = null;
                bool matches = false;

                try
                {
                    fromFile = reader.ReadToEnd(); // IOException, ObjectDisposedException, OutOfMemoryException, ArgumentOutOfRangeException

                    // The written file is in UTF8 so mimic that config
                    XmlWriterSettings xmlWriterSettings = new XmlWriterSettings()
                    {
                        // If set to true XmlWriter would close MemoryStream automatically and using would then do double dispose
                        // Code analysis does not understand that. Could need a suppress message.
                        CloseOutput = false,
                        Encoding = Encoding.UTF8,
                        OmitXmlDeclaration = false,
                        Indent = true
                    };

                    // This is not ideal but we can't really use the same classes as normal to write the XML content to the string
                    // if we want to control the encoding setting.
                    using (MemoryStream ms = new MemoryStream())
                    {
                        using (XmlWriter xw = XmlWriter.Create(ms, xmlWriterSettings)) // ArgumentNullException
                        {
                            serializer.Serialize(xw, saveData); // InvalidOperationException (see InnerException)
                        }

                        fromLiveData = Encoding.UTF8.GetString(ms.ToArray()); // ArgumentException, ArgumentNullException, DecoderFallbackException (if DecoderFallback is set to DecoderExceptionFallback)
                    }

                    // Somehow the generated string ends up with a leading null char, so need to fix it
                    fromLiveData = fromLiveData.Substring(1); // ArgumentOutOfRangeException

                    matches = fromFile != null && fromFile == fromLiveData;

                    Debug.Log($"File {fullFilename} checked against serialized original data, and {(matches ? "is confirmed to be correct" : "IS INCORRECT")}");

                    if (!matches)
                    {
                        Debug.LogError("Non matching saved data found. Here are the strings.");
                        Debug.Log("read from file: " + fromFile);
                        Debug.Log("serialized from game data: " + fromLiveData);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Exception during ValidateXmlFileAsString: {e}");
                    matches = false;
                }

                return matches;
            }
        }

        private static Dictionary<string, Thread> _threadPool = new Dictionary<string, Thread>();

        public static bool WriteFile(string filePath, XmlSerializer serializer, object saveData)
        {
            try
            {
                string folderPath = Path.GetDirectoryName(filePath); // ArgumentException, PathTooLongException

                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath); // IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException, NotSupportedException
                }

                System.Threading.ThreadPool.QueueUserWorkItem(state =>
                {
                    using (TextWriter writer = new StreamWriter(filePath))
                    {
                        serializer.Serialize(writer, saveData);
                    }
                    saveCount--;
                });
                /*
                using (TextWriter writer = new StreamWriter(filePath)) // UnauthorizedAccessException, ArgumentException, ArgumentNullException, DirectoryNotFoundException, PathTooLongException, IOException, SecurityException
                {
                    Profiler.BeginSample("Serialize");
                    serializer.Serialize(writer, saveData);
                    Profiler.EndSample();
                    // Close() apparently is old school and exists for compatibility reasons - using Dispose() is fine, even if theoretically you could have some missing logic
                } // writer.Dispose() can throw EncoderFallbackException and maybe others e.g. if disk is full
*/
                return true;
            }
            catch (UnauthorizedAccessException e)
            {
                // The caller does not have the required permission.
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnauthorizedAccessException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (PathTooLongException e)
            {
                // The specified path, file name, or both exceed the system-defined maximum length.
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (DirectoryNotFoundException e)
            {
                // The specified path is invalid (for example, it is on an unmapped drive).
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.DirectoryNotFoundException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (System.Exception e)
            {
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
        }

        /// <summary>
        /// Finds all DataManagers in loaded scene and calls their Load functions.
        /// </summary>
        /// <param name="saveDirectory">Directory where the files for the save reside</param>
        private bool LoadGameData(string saveDirectory)
        {
            Initialize();

            // Note : detect failure, but do not interrupt loading - give best effort in order to attempt to restore
            // the saved game.
            bool failure = false;

            // TODO: this probably can rely on the new cached serializers like in the save logic, but it has not been
            // tested yet, and the load spike does not matter as much as it's not happening during gameplay so I'll get
            // to this when I get to this
            DataManager[] managers = Resources.FindObjectsOfTypeAll<DataManager>();
            for (int i = 0; i < managers.Length; i++)
            {
                string fileName = GetFilePath(_baseFilename, saveDirectory, managers[i].FilePrefix);
                using (TextReader file = ReadFile(fileName))
                {
                    if (file != null)
                    {
                        if (!managers[i].Load(in file))
                        {
                            failure = true;
                            Debug.LogWarning($"File {fileName} was read but failed to load");
                        }
                    }
                    else
                    {
                        failure = true;
                        Debug.LogWarning($"File {fileName} could not be read");
                    }
                }
            }

            return !failure;
        }

        public static TextReader ReadFile(string filePath)
        {
            // If using the resource folder, use Unitys Resources.Load<> method to get data.  Path needs to just have path past Resources/ and no extension
            // When saving this isn't required as Resource path saving should only happen in editor, so full path works there
            if (filePath.Contains("Resources"))
            {
                // Warning: might not work threaded - untested
                try
                {
                    string[] spliter = { "Resources/" };
                    string[] pathSplit = filePath.Split(spliter, System.StringSplitOptions.None);

                    string resourcePath = pathSplit[pathSplit.Length - 1];

                    // Trim extension
                    int index = resourcePath.LastIndexOf('.');
                    string resourceFolderPath = index == -1 ? resourcePath : resourcePath.Substring(0, index);
                    //resourceFolderPath = resourceFolderPath.Replace('\\', '/');

                    TextAsset fileAsset = Resources.Load<TextAsset>(resourceFolderPath);
                    StringReader readerStr = new StringReader(fileAsset.text); // throws ArgumentNullException
                    return readerStr;
                }
                catch (System.Exception e)
                {
                    _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.Log($"Unable to load file \"{filePath}\", Error: {e.Message}, Inner: {e.InnerException?.Message}");
                    return null;
                }
            }
            else
            {
                try
                {
                    StreamReader reader = new StreamReader(filePath); // throws ArgumentException, ArgumentNullException, FileNotFoundException, DirectoryNotFoundException, IOException
                    return reader;
                }
                catch (System.Exception e)
                {
                    //Unable to load file "C:/Users/<USER>/AppData/LocalLow/Isto/Get To Work\SaveSlot0/gamestate_data.xml"
                    //System.IO.IOException: Sharing violation on path
                    //C:\Users\<USER>\AppData\LocalLow\Isto\Get To Work\SaveSlot0\gamestate_data.xml


                    //_errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.Log($"Unable to load file \"{filePath}\", {e.GetType()}: {e.Message}, Inner: {e.InnerException?.Message}");
                    return null;
                }
            }
        }

        public static bool SaveXMLFile<T>(string filePath, T saveData, System.Type[] extraTypes = null)
        {
            try
            {
                string folderPath = Path.GetDirectoryName(filePath); // ArgumentException, PathTooLongException

                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath); // IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException, NotSupportedException
                }

                XmlSerializer serializer = DataManager.GetXMLSerializer(saveData, extraTypes);
                using (TextWriter writer = new StreamWriter(filePath)) // UnauthorizedAccessException, ArgumentException, ArgumentNullException, DirectoryNotFoundException, PathTooLongException, IOException, SecurityException
                {
                    serializer.Serialize(writer, saveData);
                } // writer.Dispose() can throw EncoderFallbackException and maybe other exceptions, unless Remarks section is outdated. (e.g. if disk is full)

                return true;
            }
            catch (UnauthorizedAccessException e)
            {
                // The caller does not have the required permission.
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnauthorizedAccessException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (PathTooLongException e)
            {
                // The specified path, file name, or both exceed the system-defined maximum length.
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (DirectoryNotFoundException e)
            {
                // The specified path is invalid (for example, it is on an unmapped drive).
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.DirectoryNotFoundException, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
            catch (System.Exception e)
            {
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                Debug.LogError($"Unable to save file at path \"{filePath}\". {e.Message}. Inner: {e.InnerException?.Message}");
                return false;
            }
        }

        public static bool XMLFileExists(string filePath)
        {
            // If using the resource folder, use Unitys Resources.Load<> method to get data.  Path needs to just have path past Resources/ and no extension
            // When saving this isn't required as Resource path saving should only happen in editor, so full path works there
            if (filePath.Contains("Resources"))
            {
                string[] spliter = { "Resources/" };
                string[] pathSplit = filePath.Split(spliter, System.StringSplitOptions.None);

                string resourcePath = pathSplit[pathSplit.Length - 1];

                // Trim extension
                int index = resourcePath.LastIndexOf('.');
                string resourceFolderPath = index == -1 ? resourcePath : resourcePath.Substring(0, index);

                TextAsset fileAsset = Resources.Load<TextAsset>(resourceFolderPath);

                return fileAsset != null;
            }
            else
            {
                return File.Exists(filePath);
            }
        }

        public static T LoadXMLFile<T>(string filePath, System.Type[] extraTypes = null)
        {
            if (String.IsNullOrEmpty(filePath))
                return default(T);

            // If using the resource folder, use Unitys Resources.Load<> method to get data.  Path needs to just have path past Resources/ and no extension
            // When saving this isn't required as Resource path saving should only happen in editor, so full path works there
            if (filePath.Contains("Resources"))
            {
                try
                {
                    string[] spliter = { "Resources/" };
                    string[] pathSplit = filePath.Split(spliter, System.StringSplitOptions.None);

                    string resourcePath = pathSplit[pathSplit.Length - 1];

                    // Trim extension
                    int index = resourcePath.LastIndexOf('.');
                    string resourceFolderPath = index == -1 ? resourcePath : resourcePath.Substring(0, index);
                    //resourceFolderPath = resourceFolderPath.Replace('\\', '/');

                    TextAsset fileAsset = Resources.Load<TextAsset>(resourceFolderPath);

                    XmlSerializer serializer = new XmlSerializer(typeof(T), extraTypes);
                    StringReader readerStr = new StringReader(fileAsset.text); // throws ArgumentNullException

                    T data = (T)serializer.Deserialize(readerStr); // throws InvalidOperationException
                    readerStr.Close();

                    return data;
                }
                catch (System.Exception e)
                {
                    _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.Log($"Unable to load file \"{filePath}\", Error: {e.Message}, Inner: {e.InnerException?.Message}");
                    return default;
                }
            }
            else
            {
                StreamReader reader = null;
                XmlSerializer serializer = new XmlSerializer(typeof(T), extraTypes);

                try
                {
                    reader = new StreamReader(filePath); // throws ArgumentException, ArgumentNullException, FileNotFoundException, DirectoryNotFoundException, IOException

                    T data = (T)serializer.Deserialize(reader); // no exceptions thrown from this version

                    return data;
                }
                catch (System.Exception e)
                {
                    _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.Log($"Unable to load file \"{filePath}\", Error: {e.Message}, Inner: {e.InnerException?.Message}");
                    return default;
                }
                finally
                {
                    reader?.Dispose();
                }
            }
        }
        public void DuplicateGameData(int sourceSaveSlot, int destinationSaveSlot, Action<bool> onDuplicationComplete)
        {
            string saveDirectory = GetSaveDirectory(sourceSaveSlot);
            string backupDirectory = GetSaveDirectory(destinationSaveSlot);
            bool backupSuccess = CopyFiles(saveDirectory, backupDirectory);
            onDuplicationComplete?.Invoke(backupSuccess);
        }

        /// <summary>
        /// Copies folder at sourceFolder over to path backupFolder. Does not care if backupFolder exists. Will override if it does.
        /// </summary>
        /// <param name="sourceFolder"></param>
        /// <param name="backupFolder"></param>
        /// <returns>true if the folder copy was successfully created</returns>
        public static bool CopyFiles(string sourceFolder, string backupFolder)
        {
            bool success = false;

            try
            {
                // The Exists method returns false if any error occurs while trying to determine if the specified file exists.
                // This can occur in situations that raise exceptions such as passing a file name with invalid characters or
                // too many characters, a failing or missing disk, or if the caller does not have permission to read the file.
                if (System.IO.Directory.Exists(sourceFolder))
                {
                    // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException,
                    //      PathTooLongException, DirectoryNotFoundException, NotSupportedException
                    Directory.CreateDirectory(backupFolder);

                    // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException,
                    //      PathTooLongException, DirectoryNotFoundException
                    string[] files = System.IO.Directory.GetFiles(sourceFolder);

                    // Copy the files and overwrite destination files if they already exist.
                    foreach (string s in files)
                    {
                        // Use static Path methods to extract only the file name from the path.
                        string fileName = System.IO.Path.GetFileName(s); // throws ArgumentException
                        string destFile = System.IO.Path.Combine(backupFolder, fileName); // throws ArgumentException, ArgumentNullException

                        //throws UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException,
                        //     DirectoryNotFoundException, FileNotFoundException, IOException, NotSupportedException
                        System.IO.File.Copy(s, destFile, true);
                    }

                    success = true;
                }
                else
                {
                    Debug.LogError("Source path does not exist!");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"CopyFiles provoked an exception, probably trying to back up a save file: {e}");

                // Cleanup
                try
                {
                    if (System.IO.Directory.Exists(backupFolder))
                    {
                        // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException,
                        //      PathTooLongException, DirectoryNotFoundException
                        System.IO.Directory.Delete(backupFolder, recursive: true);
                    }
                }
                catch (Exception cleanupException)
                {
                    Debug.LogError($"CopyFiles could not cleanup after the exception, and caused a second exception: {cleanupException}");
                    // At this point, not much we can do, abort cleanup.
                }

                // TODO: show an error popup with the actual exceptions? Problem is this is a static class with no ability to grab the error popup
                // I could create an event to throw errors at the UI state machine
                // As long as this is used only for us to try to make backup files for the user, maybe it's ok to just fail without details?
                // But if we do that I feel we'd need a popup with an option to ignore the backup
            }

            return success;
        }

        // From IGameData
        public virtual void UpgradeGameData(int saveSlot, string targetSaveVersion, Action<bool> onUpgradeComplete)
        {

        }

        /// <summary>
        /// Checks if save files exist.
        /// Sends result via Action: True if files are in the save folder, false otherwise.
        /// </summary>
        /// <param name="onCheckComplete"></param>
        public void HasAnySaveData(Action<bool> onCheckComplete = null)
        {
            bool result = false;
            for (int i = 0; i < MaximumNumberOfRegularSaveSlots; i++)
            {
                if (HasSaveData(i))
                {
                    result = true;
                    break;
                }
            }

            onCheckComplete?.Invoke(result);
        }

        public void HasSaveData(int slotNumber, Action<bool> onCheckComplete = null)
        {
            onCheckComplete?.Invoke(HasSaveData(slotNumber));
        }

        public bool HasSaveData(int slotNumber)
        {
            bool result = false;

            if (slotNumber > MaximumNumberOfRegularSaveSlots)
            {
                // It's a special save slot
                return true;
            }

            string fileLocation = GetSaveDirectory(slotNumber);
            string fileSearch = "*" + _baseFilename;

            if (Directory.Exists(fileLocation))
            {
                try
                {
                    // throws ArgumentException, ArgumentNullException, ArgumentOutOfRangeException, UnauthorizedAccessException, DirectoryNotFoundException, PathTooLongException, IOException
                    int fileCount = Directory.GetFiles(fileLocation, fileSearch, SearchOption.TopDirectoryOnly).Length;
                    result = fileCount > 1;
                }
                catch (PathTooLongException e)
                {
                    // The specified path, file name, or both exceed the system-defined maximum length.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                    Debug.LogError($"Error checking save slot {slotNumber} at {fileLocation} for save data.  Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (IOException e)
                {
                    Debug.LogError($"Error checking save slot {slotNumber} at {fileLocation} for save data.  Exception: {e.Message}\n{e.InnerException?.Message}");
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.IOException, e);
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error checking save slot {slotNumber} at {fileLocation} for save data.  Exception: {e.Message}\n{e.InnerException?.Message}");
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                }
            }

            return result;
        }

        public bool HasSaveMetaData(int slotNumber)
        {
            string fileLocation = GetSaveDirectory(slotNumber);
            string fileSearch = "\\gamestate_data.xml";

            if (!Directory.Exists(fileLocation))
                return false;

            return File.Exists(fileLocation + fileSearch);
        }

        public void GetAllExistingSaveSlots(Action<List<int>> onListReady = null)
        {
            List<int> foundSlots = new List<int>();
            string path = GetSaveSlotsDirectory();
            string folderSearch = SaveSlotFolderPrefix + "*";

            try
            {
                //throws ArgumentException, ArgumentNullException, ArgumentOutOfRangeException, UnauthorizedAccessException, PathTooLongException, IOException, DirectoryNotFoundException
                string[] saveSlots = Directory.GetDirectories(path, folderSearch, SearchOption.TopDirectoryOnly);

                string[] separator = { SaveSlotFolderPrefix };

                foreach (string slotPath in saveSlots)
                {
                    string[] parts = slotPath.Split(separator, System.StringSplitOptions.None);

                    if (parts.Length == 0)
                    {
                        Debug.LogError($"Unexpected results in save slot lookup algorithm (offender path={slotPath})");
                    }
                    else
                    {
                        if (int.TryParse(parts[parts.Length - 1], out int slotNum))
                        {
                            foundSlots.Add(slotNum);
                        }
                        else
                        {
                            Debug.LogError($"Unexpected results in save slot lookup algorithm (offender path={slotPath})");
                        }
                    }
                }
            }
            catch (PathTooLongException e)
            {
                // The specified path, file name, or both exceed the system-defined maximum length.
                _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                Debug.LogError($"Error getting existing save slots at {path}.  Exception: {e.Message}\n{e.InnerException?.Message}");
            }
            catch (IOException e)
            {
                Debug.LogError($"Error getting existing save slots at {path}.  Exception: {e.Message}\n{e.InnerException?.Message}");
                _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.IOException, e);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error getting existing save slots at {path}.  Exception: {e.Message}\n{e.InnerException?.Message}");
                _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
            }

            onListReady?.Invoke(foundSlots);
        }

        public void ClearAllSaveData(Action<bool> onClearComplete = null)
        {
            string path = GetSaveSlotsDirectory();
            string directorySearch = "SaveSlot*";

            // This is only used internally as a tool AFAIK so I'm not bothering to handle all the exception types (there are a lot)
            try
            {
                //throws ArgumentException, ArgumentNullException, ArgumentOutOfRangeException, UnauthorizedAccessException, PathTooLongException, IOException, DirectoryNotFoundException
                string[] saveSlots = Directory.GetDirectories(path, directorySearch, SearchOption.TopDirectoryOnly);

                for (int i = 0; i < saveSlots.Length; i++)
                {
                    Debug.Log("Deleting folder " + saveSlots[i]);
                    // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException
                    Directory.Delete(saveSlots[i], recursive: true);
                }
            }
            catch
            {
                Debug.LogError("Exception happened while running ClearAllSaveData ");
            }

            onClearComplete?.Invoke(true);
        }

        public void ClearSaveData(int slotNumber, Action<bool> onClearComplete = null)
        {
            bool success = false;
            string filesLocation = GetSaveDirectory(slotNumber);
            string fileSearch = "*" + _baseFilename;
            string thumbFile = GetSaveDirectory(slotNumber) + _thumbnailFilename;

            if (!Directory.Exists(filesLocation))
            {
                onClearComplete?.Invoke(false);
                return;
            }

            Debug.Log("Deleting save slot #" + slotNumber);

            try
            {
                // throws ArgumentException, ArgumentNullException, ArgumentOutOfRangeException, UnauthorizedAccessException, DirectoryNotFoundException, PathTooLongException, IOException
                string[] saveFiles = Directory.GetFiles(filesLocation, fileSearch, SearchOption.TopDirectoryOnly);

                for (int i = 0; i < saveFiles.Length; i++)
                {
                    // throws ArgumentException, ArgumentNullException, DirectoryNotFoundException, IOException, NotSupportedException, PathTooLongException, UnauthorizedAccessException
                    File.Delete(saveFiles[i]);
                }

                if (File.Exists(thumbFile))
                {
                    // throws same as above
                    File.Delete(thumbFile);
                }

                // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException
                Directory.Delete(filesLocation);

                success = true;
            }
            catch (Exception e)
            {
                Debug.LogError("Exception when trying to delete save slot #" + slotNumber + " (" + e.Message + ")");
            }

            onClearComplete?.Invoke(success);
        }

        private void SaveScreenShot(string saveDirectory)
        {
            Texture2D screenCap = null;
            //Profiler.BeginSample("FindObjectOfType"); // 2 ms
            CameraController cameraControl = FindObjectOfType<CameraController>();
            //Profiler.EndSample();
            //Profiler.BeginSample("GetScreenCapture"); // 36ms
            if (cameraControl != null)
            {
                screenCap = cameraControl.GetScreenCapture();
            }
            else
            {
                screenCap = Camera.main.GetScreenCapture();
            }
            //Profiler.EndSample();

            if (screenCap != null)
            {
                try
                {

                    //Profiler.BeginSample("EncodeToPNG"); //166ms
                    byte[] png = screenCap.EncodeToPNG();
                    //Profiler.EndSample();
                    //Profiler.BeginSample("WriteAllBytes"); // 2.17
                    // throws ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException, IOException, UnauthorizedAccessException, NotSupportedException, SecurityException
                    File.WriteAllBytes(saveDirectory + _thumbnailFilename, png);
                    //Profiler.EndSample();

                }
                catch (PathTooLongException e)
                {
                    // The specified path, file name, or both exceed the system-defined maximum length.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (DirectoryNotFoundException e)
                {
                    // The specified path is invalid(for example, it is on an unmapped drive).
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.DirectoryNotFoundException, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (IOException e)
                {
                    // An I/O error occurred while opening the file.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.IOException, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (UnauthorizedAccessException e)
                {
                    /*
                    path specified a file that is read-only.
                    -or-
                    path specified a file that is hidden.
                    -or-
                    This operation is not supported on the current platform.
                    -or-
                    path specified a directory.
                    -or-
                    The caller does not have the required permission.
                     */
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnauthorizedAccessException, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (SecurityException e)
                {
                    // The caller does not have the required permission.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.SecurityException, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (Exception e)
                {
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.LogError($"Something went wrong when trying to save thumbnail at \"{saveDirectory}\". Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
            }
        }

        public void LoadThumbnailForSaveSlot(int slotNumber, Action<Texture2D> onThumbnailReady = null)
        {
            Texture2D imageTexture = null;

            string fileName = GetSaveDirectory(slotNumber) + _thumbnailFilename;

            if (File.Exists(fileName))
            {
                try
                {
                    // throws ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException, IOException, UnauthorizedAccessException, FileNotFoundException, NotSupportedException, SecurityException
                    byte[] imageBytes = File.ReadAllBytes(fileName);

                    imageTexture = new Texture2D(2, 2);
                    imageTexture.LoadImage(imageBytes);
                }
                catch (PathTooLongException e)
                {
                    // The specified path, file name, or both exceed the system-defined maximum length.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.PathTooLongException, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (DirectoryNotFoundException e)
                {
                    // The specified path is invalid(for example, it is on an unmapped drive).
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.DirectoryNotFoundException, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (IOException e)
                {
                    // An I/O error occurred while opening the file.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.IOException, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (UnauthorizedAccessException e)
                {
                    /*
                     * This operation is not supported on the current platform.
                        - or -
                        path specified a directory.
                        - or -
                        The caller does not have the required permission.
                     */
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnauthorizedAccessException, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (SecurityException e)
                {
                    // The caller does not have the required permission.
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.SecurityException, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                catch (Exception e)
                {
                    _errorPopup.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);
                    Debug.LogError($"Something went wrong when trying to read thumbnail for save slot {slotNumber}. Exception:{e.Message} (inner:{(e.InnerException != null ? e.InnerException.Message : "null")})");
                }
                finally
                {

                }
            }

            onThumbnailReady?.Invoke(imageTexture);
        }

        private string GetSaveSlotsDirectory()
        {
            switch (_saveSlotLocation)
            {
                case IGameData.GameDataLocation.ResourcesFolder:
                    return Application.dataPath + "/Resources/InternalSaveSlots";
                case IGameData.GameDataLocation.PersistentDataPath:
                default:
                    return Application.persistentDataPath;
            }
        }

        public string GetSaveDirectory(int slotNumber)
        {
            return GetSaveSlotsDirectory() + $"\\{SaveSlotFolderPrefix}{slotNumber}";
        }

        public void GetLowestAvailableSlotNumber(Action<int> onSlotNumberFound = null)
        {
            int result = -1;
            for (int i = 0; i < MaximumNumberOfRegularSaveSlots; i++)
            {
                // checking HasSaveMetaData is more efficient, but very old save slots might not have the meta data file
                if (!HasSaveData(i))
                {
                    result = i;
                    break;
                }
            }

            if (result == -1)
                Debug.LogWarning("User already has 50 save slots");

            onSlotNumberFound?.Invoke(result);
        }

        public void GetNextAvailableSlotNumber(int previousSlot, Action<int> onNextSlotFound)
        {
            for (int i = previousSlot; i < MaximumNumberOfRegularSaveSlots; i++)
            {
                if (!HasSaveMetaData(i)) // TODO: check HasSaveData instead? if it's more efficient
                {
                    onNextSlotFound.Invoke(i);
                    return;
                }
            }

            GetLowestAvailableSlotNumber(onNextSlotFound);
        }

        // TODO: ideally instead of the actual disk space we should show what's available for cloud saving
        public void GetRemainingSaveFileSpace(Action<long> onFileSpaceObtained = null)
        {
            long availableBytes = 0;

            try
            {
                DriveInfo info = GetSaveSlotsDriveInfo(); // throws IOException, UnauthorizedAccessException
                availableBytes = info.AvailableFreeSpace; // throws UnauthorizedAccessException, IOException
            }
            catch (IOException e) // An I/ O error occurred(for example, a disk error or a drive was not ready).
            {
                Debug.LogError($"IOException during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }
            catch (UnauthorizedAccessException e) // The caller does not have the required permission. Access to the drive information is denied.
            {
                Debug.LogError($"UnauthorizedAccessException during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }
            catch (Exception e)
            {
                // This would be highly unexpected but it's not worth breaking game execution for an issue getting drive info
                Debug.LogError($"Exception during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }

            onFileSpaceObtained?.Invoke(availableBytes);
        }

        // TODO: ideally instead of the actual disk space we should show what's available for cloud saving
        public void GetMaximumSaveFileSpace(Action<long> onMaxFileSpaceObtained = null)
        {
            long maximumBytes = 0;

            try
            {
                DriveInfo info = GetSaveSlotsDriveInfo(); // throws IOException, UnauthorizedAccessException
                maximumBytes = info.TotalSize; // throws UnauthorizedAccessException, DriveNotFoundException, IOException
            }
            catch (DriveNotFoundException e) // The drive is not mapped or does not exist.
            {
                Debug.LogError($"UnauthorizedAccessException during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }
            catch (IOException e) // An I/ O error occurred (for example, a disk error or a drive was not ready).
            {
                Debug.LogError($"IOException during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }
            catch (UnauthorizedAccessException e) // The caller does not have the required permission. Access to the drive information is denied.
            {
                Debug.LogError($"UnauthorizedAccessException during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }
            catch (Exception e)
            {
                // This would be highly unexpected but it's not worth breaking game execution for an issue getting drive info
                Debug.LogError($"Exception during GetRemainingSaveFileSpace: {e.Message} (Inner: {(e.InnerException != null ? e.InnerException.Message : "null")}) Stack:{e.StackTrace}");
            }

            onMaxFileSpaceObtained?.Invoke(maximumBytes);
        }

        /// <summary>
        /// Throws IOException and UnauthorizedAccessException!
        /// </summary>
        /// <returns></returns>
        private DriveInfo GetSaveSlotsDriveInfo()
        {
            DriveInfo result = null;
            string writeDestination = GetSaveSlotsDirectory();
            string driveLetter = writeDestination.Substring(0, 1);

            DriveInfo[] allDrives = DriveInfo.GetDrives(); // throws IOException, UnauthorizedAccessException

            foreach (DriveInfo d in allDrives)
            {
                if (d.IsReady && d.Name.StartsWith(driveLetter))
                {
                    result = d;
                }
            }

            return result;
        }

        // Custom Controls Data

        public void HasCustomControlsData(Action<bool> onCheckComplete = null)
        {
            string filePath = GetFilePath(GetControlsSaveFilename(), Application.persistentDataPath + "/", "");

            onCheckComplete?.Invoke(XMLFileExists(filePath));
        }

        public void SaveCustomControlMappings(PlayerCustomControlsSaveData controlSaveData)
        {
            string filePath = GetFilePath(GetControlsSaveFilename(), Application.persistentDataPath + "/", "");
            SaveXMLFile(filePath, controlSaveData);
        }

        public void LoadCustomControlMappings(Action<PlayerCustomControlsSaveData> onLoadComplete)
        {
            string filePath = GetFilePath(GetControlsSaveFilename(), Application.persistentDataPath + "/", "");

            if (XMLFileExists(filePath))
            {
                PlayerCustomControlsSaveData controlsSaveData = LoadXMLFile<PlayerCustomControlsSaveData>(filePath);

                onLoadComplete(controlsSaveData);
                return;
            }
            else
            {
                onLoadComplete(null);
            }
        }

        private readonly static string NO_FILE_PREFIX = "/";

        public void SaveSpeedrunTimes(SpeedrunTimerData timerData, Action<bool> onSaveComplete)
        {
            string fileName = _speedrunSettings.GetPersonalBestsFileName();
            string saveDirectory = GetSaveSlotsDirectory();
            string fullFilename = GetFilePath(fileName, saveDirectory, NO_FILE_PREFIX);
            XmlSerializer serializer = new XmlSerializer(typeof(SpeedrunTimerData));
            bool success = WriteFile(fullFilename, serializer, timerData);
            SaveGoldSplits(timerData);
            onSaveComplete?.Invoke(success);
        }

        private void SaveGoldSplits(SpeedrunTimerData newTimerData)
        {
            LoadGoldSplits(existingGoldSplits =>
            {
                SpeedrunTimerData goldSplitsToSave = new SpeedrunTimerData();

                if (existingGoldSplits != null && existingGoldSplits.times != null)
                {
                    // Use existing gold splits as base
                    goldSplitsToSave.times = new float[existingGoldSplits.times.Length];
                    for (int i = 0; i < existingGoldSplits.times.Length; i++)
                    {
                        goldSplitsToSave.times[i] = existingGoldSplits.times[i];
                    }
                }
                else
                {
                    // No existing gold splits, initialize with new data
                    goldSplitsToSave.times = new float[newTimerData.times.Length];
                    for (int i = 0; i < newTimerData.times.Length; i++)
                    {
                        goldSplitsToSave.times[i] = newTimerData.times[i];
                    }
                }

                // Ensure arrays are the same length
                if (goldSplitsToSave.times.Length != newTimerData.times.Length)
                {
                    float[] resizedArray = new float[newTimerData.times.Length];
                    for (int i = 0; i < newTimerData.times.Length; i++)
                    {
                        if (i < goldSplitsToSave.times.Length)
                        {
                            resizedArray[i] = goldSplitsToSave.times[i];
                        }
                        else
                        {
                            resizedArray[i] = newTimerData.times[i];
                        }
                    }
                    goldSplitsToSave.times = resizedArray;
                }

                // Compare and update with faster times
                for (int i = 0; i < newTimerData.times.Length; i++)
                {
                    if (newTimerData.times[i] > 0f &&
                        (goldSplitsToSave.times[i] <= 0f || newTimerData.times[i] < goldSplitsToSave.times[i]))
                    {
                        goldSplitsToSave.times[i] = newTimerData.times[i];
                    }
                }

                // Save the updated gold splits
                string fileName = _speedrunSettings.GetGoldSplitsFileName();
                string saveDirectory = GetSaveSlotsDirectory();
                string fullFilename = GetFilePath(fileName, saveDirectory, NO_FILE_PREFIX);
                XmlSerializer serializer = new XmlSerializer(typeof(SpeedrunTimerData));
                bool success = WriteFile(fullFilename, serializer, goldSplitsToSave);

                if (success)
                {
                    Debug.Log("Gold splits updated successfully.");
                }
                else
                {
                    Debug.LogError("Failed to save gold splits.");
                }
            });
        }

        public void LoadGoldSplits(Action<SpeedrunTimerData> onLoadComplete)
        {
            SpeedrunTimerData loadedData = null;
            string fileName = _speedrunSettings.GetGoldSplitsFileName();
            string saveDirectory = GetSaveSlotsDirectory();
            string fullFilename = GetFilePath(fileName, saveDirectory, NO_FILE_PREFIX);
            if (XMLFileExists(fullFilename))
            {
                using (TextReader reader = ReadFile(fullFilename))
                {
                    if (reader != null)
                    {
                        XmlSerializer serializer = new XmlSerializer(typeof(SpeedrunTimerData));

                        try
                        {
                            loadedData = (SpeedrunTimerData)serializer.Deserialize(reader); // throws InvalidOperationException
                        }
                        catch (InvalidOperationException e)
                        {
                            Debug.LogError("caught InvalidOperationException: " + e.InnerException.Message);
                        }
                    }
                }
            }
            onLoadComplete?.Invoke(loadedData);
        }

        public void LoadSpeedrunTimes(Action<SpeedrunTimerData> onLoadComplete)
        {
            SpeedrunTimerData loadedData = null;
            string fileName = _speedrunSettings.GetPersonalBestsFileName();
            string saveDirectory = GetSaveSlotsDirectory();
            string fullFilename = GetFilePath(fileName, saveDirectory, NO_FILE_PREFIX);
            if (XMLFileExists(fullFilename))
            {
                using (TextReader reader = ReadFile(fullFilename))
                {
                    if (reader != null)
                    {
                        XmlSerializer serializer = new XmlSerializer(typeof(SpeedrunTimerData));

                        try
                        {
                            loadedData = (SpeedrunTimerData)serializer.Deserialize(reader); // throws InvalidOperationException
                        }
                        catch (InvalidOperationException e)
                        {
                            Debug.LogError("caught InvalidOperationException: " + e.InnerException.Message);
                        }
                    }
                }
            }
            onLoadComplete?.Invoke(loadedData);
        }

        private string GetControlsSaveFilename()
        {
            string controlsFilename = _customControlsSaveFilename;
            if (controlsFilename.IndexOf(".xml", StringComparison.OrdinalIgnoreCase) < 0)
            {
                controlsFilename += ".xml";
            }

            return controlsFilename;
        }

#if UNITY_EDITOR
        public void SetSaveFolderToResources()
        {
            _saveSlotLocation = IGameData.GameDataLocation.ResourcesFolder;
        }
#endif
    }
}