// Copyright Isto Inc.

using Isto.Core.Configuration;
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUIGameModeDisplay : MonoBehaviour
    {
        //UNITY HOOKUP

        [SerializeField] private GameModeDefinition _gameMode = null;
        [SerializeField] private CoreUISetGameModeSubState _parentMenu = null;
        [SerializeField] private Button _modeButton = null;
        [SerializeField] private RectTransform _disabledOverlay = null;

        [SerializeField] protected TextMeshProUGUI modeNameLabel;
        [SerializeField] protected TextMeshProUGUI modeDescriptionLabel;
        [SerializeField] protected HorizontalLayoutGroup statusLayoutGroup;
        [SerializeField] protected TextMeshProUGUI dateLabel;


        // INJECTION

        protected ILocalizationProvider _localization;
        protected LocTerm.Factory _localizedStringFactory;

        [Inject]
        public void Inject(ILocalizationProvider localization, LocTerm.Factory localizedFactory)
        {
            _localization = localization;
            _localizedStringFactory = localizedFactory;
        }


        // LIFECYCLE EVENTS

        protected virtual void OnEnable()
        {
            // Game Mode Title
            LocExpression gameModeTitle = CreateLocExpressionForGameMode(_gameMode.NameLoc.mTerm, _gameMode.internalName);
            gameModeTitle.LocalizeInto(modeNameLabel);

            // Game Mode Description
            LocExpression gameModeDescription = CreateLocExpressionForGameMode(_gameMode.DescriptionLoc.mTerm, _gameMode.internalName);
            gameModeDescription.LocalizeInto(modeDescriptionLabel);

            if (statusLayoutGroup != null)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(statusLayoutGroup.transform as RectTransform);
            }

            // Set availability
            bool allowed = _gameMode != null && _gameMode.Allowed;

            if (_disabledOverlay != null)
            {
                _disabledOverlay.gameObject.SetActive(!allowed);
            }
            
            if (_modeButton != null)
            {
                _modeButton.interactable = allowed;
            }
        }


        // OTHER METHODS

        public void SelectModeButtonClicked()
        {
            _parentMenu.ModeButtonClicked(_gameMode);
        }


        private LocExpression CreateLocExpressionForGameMode(string localizedTerm, string fallbackTerm)
        {
            LocTerm.LocalizationType type = LocTerm.LocalizationType.Localized;
            string term = localizedTerm;

            if (string.IsNullOrEmpty(localizedTerm))
            {
                type = LocTerm.LocalizationType.NonLocalized;
                term = fallbackTerm;
            }

            LocTerm gameModeExpression = _localizedStringFactory.Create(type, term);
            return new SingleTermLocExpression(gameModeExpression);
        }
    }
}