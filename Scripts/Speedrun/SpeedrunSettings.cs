// Copyright Isto Inc.
using Isto.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.Speedrun
{
    /// <summary>
    /// This asset defines all speedrun config details, like user options, defaults, names, icons, colors, loc, etc.
    /// This asset is optional, if it exists, it indicates that you want speedrun timer in your project.
    /// Is is intended to be in the Resources folder with the reserved name specified in ASSET_NAME
    /// SpeedrunSettings should be generally injected as optional as well so modules that depend on it can resolve when
    /// it is missing (e.g. in a new project).
    /// </summary>
    [CreateAssetMenu(fileName = COMPILED_ASSET_NAME, menuName = "Scriptables/SpeedrunSettings")]
    public class SpeedrunSettings : ScriptableObject
    {
        /// <summary>
        /// Defines the supported modular sections of our speedrun timer widget so that we can organize them in data.
        /// This means that their order matters, but they cannot be added more than once at the moment. Also, EMPTY is
        /// just for representation and does nothing.
        /// I considered using Flags, but in that case, re-ordering makes less sense. I am also unsure if in the future
        /// certain sections might be added more than once. The values are flags-compatible just in case we change it.
        /// </summary>
        public class SpeedrunTimerSectionEnum : Int32Enum<SpeedrunTimerSectionEnum>
        {
            public static readonly SpeedrunTimerSectionEnum EMPTY = new SpeedrunTimerSectionEnum(0, nameof(EMPTY));
            public static readonly SpeedrunTimerSectionEnum MAIN_TIMER = new SpeedrunTimerSectionEnum(1, nameof(MAIN_TIMER));
            public static readonly SpeedrunTimerSectionEnum RUN_DETAILS = new SpeedrunTimerSectionEnum(2, nameof(RUN_DETAILS));
            public static readonly SpeedrunTimerSectionEnum CHAPTER_INFO = new SpeedrunTimerSectionEnum(4, nameof(CHAPTER_INFO));
            public static readonly SpeedrunTimerSectionEnum FULL_DISPLAY = new SpeedrunTimerSectionEnum(8, nameof(FULL_DISPLAY));

            public SpeedrunTimerSectionEnum(int value, string name) : base(value, name)
            {
            }
        }

        /// <summary>
        /// Defines what we display in the main timer label of the segments.
        /// Does not affect the main timer of the widget.
        /// </summary>
        public enum SpeedrunFullSectionListTypeEnum
        {
            ListOfAbsoluteTimes, // default - splits keep going from the previous segment's time
            ListOfRelativeTimes, // each split starts from zero

            // These two would not work great if you don't have a PB to display
            ListOfPersonalBestTimes, // constant target in the segment timer - infer your time from your delta
            ListOfDiffsFromPersonalBest // show the delta permanently (will count down)
        }

        /// <summary>
        /// Defines where we should attempt to save the player's PB when it gets requested.
        /// This is more of a PC setting, but on console, we expect the save to be handled in an equivalent way which
        /// may be slightly different (e.g. LocalLow on xbox would end up as a unique user-tied blob of cloud data).
        /// </summary>
        public enum SpeedrunPersonalBestsSaveLocationEnum
        {
            DoNotSave = 0,
            SaveSlot,
            LocalLow // Should be part of the cloud-saved files for platforms that support it!
        }

        // The enum wrappers are to allow using our EnumDropdown property drawer on elements from a list.
        // The decorator would not work on a List<int>. We probably could make a new one for this use case.
        // I suppose it will come up again so it would be worth putting some time into that when we can.
        [Serializable]
        public class SpeedrunTimerSectionWrapper
        {
            [EnumDropdown(typeof(SpeedrunTimerSectionEnum))]
            public int timerSectionValue;
        }

        /// <summary>
        /// Represents a timer "display option", in other words this is a set of instructions for the UI to respect.
        /// We can configure several timer styles using this data structure and let the user toggle between them for
        /// the one that best meets their preferences.
        /// </summary>
        [Serializable]
        public class SpeedrunTimerConfig
        {
            public string internalName;
            public string setupNameLocKey;

            [Header("Sections - one of each max - reordering not supported yet")]
            public List<SpeedrunTimerSectionWrapper> timerSectionsToShow;

            [Header("Run Details options")]
            // this shows PB in run info section
            public bool showGameName;
            public bool showRunCategory;
            public bool showTotalPersonalBest;

            [Header("Chapter Details options")]
            public bool showCurrentChapterBestTime;

            [Header("Full Display options")]
            public SpeedrunFullSectionListTypeEnum segmentTimersType;

            public bool showChapterIcons;
            public bool showUpcomingChapters;
            public bool userCanScrollTimesList;
            public bool autoScrollTimesListOnSplitChange;
            public bool autoResizeTimesListPanel;

            [Tooltip("This makes the timer change color if the player's time becomes behind the Target time")]
            public bool showPBColorsInSegmentTime;
            [Tooltip("This adds a column to the left of the segment time to display the diff from your PB as you " +
                     "finish segments")]
            public bool showPersonalBestDeltas;
            public bool cumulativePersonalBestDeltas;

            // redundant with SpeedrunTimerStyleEnum.ListOfPersonalBestTimes, don't use both
            [Tooltip("This adds a column to the right to display the PB times")]
            public bool showPersonalBestColumn;

            [Header("Bottom extras")]
            [Tooltip("This controls if a toggle button is shown at the bottom of the timer widget")]
            public bool showStyleToggleButton;
            [Tooltip("This controls if a save button is shown at the bottom of the timer widget")]
            public bool showPersonalBestSaveButton;

            public bool IsSectionShown(SpeedrunTimerSectionEnum section)
            {
                return timerSectionsToShow.Where(x => x.timerSectionValue == section.Value).Any();
            }
        }

        /// <summary>
        /// This is a color set for information relative to the PB times in the timer UI. Bundled for convenience.
        /// We might need to add options for coloring the Target times differently as well.
        /// This data is in the SpeedrunSettings object scope at the moment but could be moved to SpeedrunTimerSetup
        /// if we want to give color alternatives to the player in our style options.
        /// </summary>
        [Serializable]
        public class PBStyleSettings
        {
            [Header("Warning - the 3 following settings are not implemented but they are often used in livesplit")]
            public Color belowPersonalBest; // 'gold' split, normally... gold
            public bool flashTextWhenBelowPersonalBest;
            public Color belowPersonalBestFlashColor; // most of the time this should be white
            [Space]
            public Color belowTarget; // normally green
            public Color exactTarget; // edge case. not sure what's normal, I'm assuming white like regular text
            public Color passedTarget; // normally red
        }

        /// <summary>
        /// This is a loc key set for messages relative to the PB times in the timer UI. Bundled for convenience.
        /// </summary>
        [Serializable]
        public class PBLocKeys
        {
            [Header("Save Popup")]
            public string savePopupTitle;
            public string savePopupQuestion; // Do you want to save?
            public string savePopupInfo; // e.g. override warning

            [Header("Confirm Popup")]
            public string confirmPopupTitle; // Save success
            public string confirmPopupCloud;
            public string confirmPopupFile;
            public string confirmPopupFolder;

            [Header("Other")]
            public string chapterLabel;
            public string bestChapterTimeLabel;
            public string bestRunTimeLabel;
        }


        // UNITY HOOKUP

        [Header("Timer UI Configuration")]
        [SerializeField] private string _gameName = "Atrio";
        [SerializeField] private string _emptyTimerDefaultText = "--:--:--";
        [Tooltip("This is optional. We will show the internal split names if the titles are not defined.")]
        [SerializeField] private string[] _chapterTitleLocKeys;
        [Tooltip("This is optional.")]
        [SerializeField] private Sprite[] _chapterSprites;
        [FormerlySerializedAs("_styleOptions")]
        [SerializeField] private SpeedrunTimerConfig[] _timerConfigs;

        [Header("Main Timer Configuration")]
        [SerializeField] private Color _normalText;
        [SerializeField] private Color _notReadyText;

        [Header("Personal Best Configuration")]
        [FormerlySerializedAs("_personalBestsSaving")]
        [SerializeField] private SpeedrunPersonalBestsSaveLocationEnum _personalBestsSavingLocation;
        [SerializeField] private string _personalBestsFileName = "best_split_times.txt";
        [SerializeField] private PBStyleSettings _personalBestsStyleSettings;
        [SerializeField] private PBLocKeys _personalBestsLocKeys;

        [Header("Game Settings Menu Options")]
        [SerializeField] private bool _userCanTurnOnTimer;
        [SerializeField] private bool _userCanRelocate;
        [SerializeField] private bool _userCanResize;
        [SerializeField] private bool _userCanAdjustTransparency;
        [SerializeField] private bool _userCanChangeStyle;
        [SerializeField] private bool _userCanAssignStyleToggleButton;

        [Header("Default Settings")]
        [Tooltip("Not to be confused with the hidden option in the timer configs list. The timer can be turned on but "
               + "hidden, to the user it's the same but your project might only support one.")]
        [SerializeField] private bool _timerIsOn;
        [FormerlySerializedAs("_speedrunTimerStyleSetupIndex")]
        [SerializeField] private int _timerConfigIndex = 0; // by convention usually 0 is the index of the "hidden" config.
        [SerializeField][Range(0f, 1f)] private float _topToBottomOffset = 0f;
        [SerializeField][Range(0f, 1f)] private float _leftToRightOffset = 1f;

        // Planned features
        [SerializeField][Range(0.25f, 2f)] private float _uiPanelSizeMultiplier = 1f; // might be a pain to anchor...
        [SerializeField][Range(0.1f, 1f)] private float _uiPanelAlpha = 1f; // might help if timer feels too intrusive


        // OTHER FIELDS

        private const string COMPILED_ASSET_NAME = "SpeedrunSettings"; // Only for CreateAssetMenu

        public static readonly string ASSET_NAME = COMPILED_ASSET_NAME;
        public static readonly string MENU_ACTIVE_PLAYER_PREFS_KEY = "SpeedRunMenuSetting";
        public static readonly string CONFIG_PLAYER_PREFS_KEY = "SpeedRunMenuStyleSetting";
        public static readonly string HORIZONTAL_POS_PLAYER_PREFS_KEY = "SpeedRunHPositionSetting";
        public static readonly string VERTICAL_POS_PLAYER_PREFS_KEY = "SpeedRunVPositionSetting";

        // PROPERTIES
        private string PersonalGoldSplitsFileName => "gold_splits_" + _personalBestsFileName;


        // LIFECYCLE EVENTS

        private void OnValidate()
        {
            if (_personalBestsStyleSettings.flashTextWhenBelowPersonalBest)
            {
                _personalBestsStyleSettings.flashTextWhenBelowPersonalBest = false;
                Debug.LogError("Flashing Text For Personal Best Splits is not supported yet.");
            }

            if (_userCanResize)
            {
                _userCanResize = false;
                Debug.LogError("User Can Resize setting is not supported yet.");
            }

            if (_userCanAdjustTransparency)
            {
                _userCanAdjustTransparency = false;
                Debug.LogError("User Can Adjust Transparency setting is not supported yet.");
            }

            if (_userCanAssignStyleToggleButton)
            {
                _userCanAssignStyleToggleButton = false;
                Debug.LogError("User Can Assign Style Toggle Button setting is not supported yet.");
            }

            if (_personalBestsSavingLocation == SpeedrunPersonalBestsSaveLocationEnum.SaveSlot)
            {
                _personalBestsSavingLocation = SpeedrunPersonalBestsSaveLocationEnum.DoNotSave;
                Debug.LogError("Personal Bests Saved In Save Slot is not supported yet.");
            }
        }


        // ACCESSORS

        public bool GetDefaultTimerIsOnSetting()
        {
            return _timerIsOn;
        }

        public SpeedrunTimerConfig GetTimerConfig(int index)
        {
            return _timerConfigs[index];
        }

        public SpeedrunTimerConfig GetTimerConfig(string internalName)
        {
            SpeedrunTimerConfig value = null;

            for (int i = 0; i < _timerConfigs.Length; i++)
            {
                if (_timerConfigs[i].internalName == internalName)
                {
                    value = _timerConfigs[i];
                    break;
                }
            }

            return value;
        }

        public SpeedrunTimerConfig GetDefaultTimerConfig()
        {
            return GetTimerConfig(GetDefaultTimerConfigIndex());
        }

        public SpeedrunTimerConfig GetPlayerSelectedTimerConfig()
        {
            return GetTimerConfig(GetPlayerSelectedConfigIndex());
        }

        /// <summary>
        /// Finds the timer config among the existing options that corresponds to the provided lockey.
        /// Also will find it if lockey happens to be the internalName instead of the loc key, which we support for
        /// when developers are putting test setups together and don't want to necessarily add a loc key to the DB.
        /// This should not cause any accidents as loc keys are supposed to look very different from internal names.
        /// </summary>
        /// <param name="locKey">The loc key (or internal name) of the config option that you want.</param>
        /// <returns>The index of the config option that you requested, or -1 if not found.</returns>
        public int GetTimerConfigIndex(string locKey)
        {
            int index = -1;

            for (int i = 0; i < _timerConfigs.Length; i++)
            {
                if (_timerConfigs[i].setupNameLocKey == locKey
                    || _timerConfigs[i].internalName == locKey) // backup system for when we test some configs
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        public int GetDefaultTimerConfigIndex()
        {
            return _timerConfigIndex;
        }

        public int GetPlayerSelectedConfigIndex()
        {
            int configIndex = PlayerPrefs.GetInt(CONFIG_PLAYER_PREFS_KEY, -1);
            if (configIndex == -1)
            {
                int defaultIndex = GetDefaultTimerConfigIndex();
                PlayerPrefs.SetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, defaultIndex);
                configIndex = defaultIndex;
            }

            return configIndex;
        }

        public int GetTimerConfigsCount()
        {
            return _timerConfigs.Length;
        }

        public Color GetNormalTimerTextColor()
        {
            return _normalText;
        }

        public Color GetUninitializedTimerTextColor()
        {
            return _notReadyText;
        }

        // Not sure if there is a place I should get this (where it's defined already) instead of it being a setting.
        // For now this ensures you can update it per game and that it's not localized.
        public string GetGameName()
        {
            return _gameName;
        }

        /// <summary>
        /// Provides you with text to put in timers that have no value yet so they look good in the meantime.
        /// </summary>
        /// <returns>The string to use as default value in timer labels.</returns>
        public string GetDefaultTimerText()
        {
            return _emptyTimerDefaultText;
        }

        public Sprite GetIcon(int chapter)
        {
            if (_chapterSprites == null || _chapterSprites.Length <= (chapter + 1))
                return null;

            return _chapterSprites[chapter];
        }

        public string GetLocalizationKey(int chapter)
        {
            if (_chapterTitleLocKeys == null || _chapterTitleLocKeys.Length <= (chapter + 1))
                return null;

            return _chapterTitleLocKeys[chapter];
        }

        public bool CanUserToggleTimer()
        {
            return _userCanTurnOnTimer;
        }

        public bool CanUserChangeTimerPosition()
        {
            return _userCanRelocate;
        }

        public bool CanUserChangeTimerStyle()
        {
            return _userCanChangeStyle;
        }

        public float GetDefaultScreenWidthRatio()
        {
            return _leftToRightOffset;
        }

        public float GetDefaultScreenHeightRatio()
        {
            return _topToBottomOffset;
        }

        public SpeedrunPersonalBestsSaveLocationEnum GetPersonalBestSaveLocation()
        {
            return _personalBestsSavingLocation;
        }

        public string GetPersonalBestsFileName()
        {
            return _personalBestsFileName;
        }

        public string GetGoldSplitsFileName()
        {
            return PersonalGoldSplitsFileName;
        }

        public PBStyleSettings GetPersonalBestsStyleSettings()
        {
            return _personalBestsStyleSettings;
        }

        public PBLocKeys GetPersonalBestsLocKeys()
        {
            return _personalBestsLocKeys;
        }


        // OTHER METHODS

        /// <summary>
        /// This is a tool to get the speedrun settings config from the editor.
        /// At runtime, you should use injection.
        /// </summary>
        public static SpeedrunSettings LoadFromResources()
        {
            SpeedrunSettings settings = Resources.Load<SpeedrunSettings>(ASSET_NAME);
            if (settings == null)
            {
                Debug.LogError($"Could not find {ASSET_NAME} file in the project");
            }
            return settings;
        }
    }
}